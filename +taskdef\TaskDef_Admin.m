classdef TaskDef_Admin<daemon.taskdef.TaskDef
    %UNTITLED3 Summary of this class goes here
    %   Detailed explanation goes here

    properties

    end

    methods
        function obj = TaskDef_Admin(varargin)
            <EMAIL>(varargin{:});
        end

    end

    methods(Static)
        function Scheduled(tempfolder, taskdefname, RecurringPeriod, RecurringPeriodUnit,taskfilename)
          %            "TaskName":"Scheduled",
          % "TaskUID": "ScheduleCheckStatus_1min",
          % "TaskDef": {
          %   "IsAdminTask": 2,
          %   "Schedule": {
          %     "RecurringPeriod": 1,
          %     "RecurringPeriodUnit": "minute"
          %   },
          %   "PostTask": {
          %     "taskfilename": "CheckStatus/EVERYONE.tsk"
          %   }
          % },
          % "LastExecutionDataTime": "20230429T002305"
            TaskName = "Scheduled";
            TaskUID  = ['Schedule_' taskdefname '_' num2str(RecurringPeriod) RecurringPeriodUnit];
            Schedule = struct('RecurringPeriod', RecurringPeriod, 'RecurringPeriodUnit', RecurringPeriodUnit);
            LastExecutionDataTime=datestr(now, 'yyyymmddTHHMMSS');
            obj = daemon.taskdef.TaskDef({'TaskDefFolder', tempfolder}, ...
                    {'TaskDefName', taskdefname});
            obj.m_cfgfile = [tempfolder TaskUID '.tsk'];
            Process=struct('OperationType', 'CreateTaskFile', "TaskFileName", taskfilename);
            % obj.Generate([], [], Process,{'IsAdminTask', 2}, {'TaskName', TaskName}, {'TaskUID', TaskUID},...
            %     {'Schedule', Schedule}, {'LastExecutionDataTime',  LastExecutionDataTime});
            TaskDef = struct('Process', Process, 'IsAdminTask', 2,'Schedule', Schedule);
            obj.Generate([], [], [], {'TaskName', TaskName}, {'TaskUID', TaskUID},...
                 {'LastExecutionDataTime',  LastExecutionDataTime}, {'TaskDef', TaskDef});
        end

        function ExecuteCommand(tasksdeffile, taskdefname, CommandFile, WorkingDir, varargin)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            Process = struct('OperationType', 'ExecuteCommand', ...
                "CommandFile",CommandFile, "CommandType", 'cmd', "WorkingDir",  WorkingDir);
            obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, [], Process,{'IsAdminTask', 1}, varargin{:});
        end

        function Service_restart(tasksdeffile, servicename,varargin)
            
            taskdefname= ['restart_' servicename];
            CommandFile= ['../' servicename '/restart.cmd'];
            WorkingDir = ['../' servicename '/'];
            daemon.taskdef.TaskDef_Admin.ExecuteCommand(tasksdeffile, taskdefname, CommandFile, WorkingDir, varargin{:});
        end

        function Factory_Services(rootfolder,  varargin)
            tasksdeffile = [rootfolder  'ServiceAdmin\tasksdef\tasks_services.def'];
            servicenames = {'ServiceAdmin', 'ServiceAtlas', 'ServiceDcmServer',  'ServiceDcmConverter', ...
                'ServiceDoseDaemon', 'ServiceFocal', 'ServiceRTPlan', 'ServiceWasm', 'ServiceGroup'};

            for k=1:numel(servicenames)
                daemon.taskdef.TaskDef_Admin.Service_restart(tasksdeffile, servicenames{k},varargin{:});
            end
        end

        function RemoveFile(tasksdeffile, taskdefname, filepatterns,filterinfo, varargin)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            if ischar(filepatterns)
                filepatterns=strsplit(filepatterns, '|');
            end
            for m=1:numel(filepatterns)
                Process{m} = struct('OperationType', 'RemoveFile', ...
                'RootFolder', '../', 'FilePattern', filepatterns{m}, 'FilterInfo',filterinfo );
            end
            obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, [], Process,{'IsAdminTask', 1}, varargin{:});
        end
        
        function RemoveTasks(tasksdeffile, taskdefname, ageindays, varargin)
            %tasksdeffile='C:\ARTDaemon\SegmanRSH\ServiceAdmin\tasksdef\tasks_file.def';
            %taskdefname ='RemoveTasksOlderThanOneHour';
            filepattern ='Tasks/**/*.tsk|Tasks/**/*.tsks|Tasks/**/*.tskinfo|Tasks/**/*_tsk.xlsx';
            DateTimeRange=[];
            if exist('ageindays', 'var') && ~isempty(ageindays)
                DateTimeRange=[-100000 -ageindays];
            end
            filterinfo  = struct('FileNamePattern', '^(?!.*Admin.*Scheduled).*$', 'DateTimeRange', DateTimeRange);
            daemon.taskdef.TaskDef_Admin.RemoveFile(tasksdeffile, taskdefname, filepattern,filterinfo, varargin{:});
        end

        function Factory_RemoveTasks(rootfolder,  varargin)
            tempfolder = [rootfolder 'template\ServiceAdmin\'];
            tasksdeffile=[rootfolder  'ServiceAdmin\tasksdef\tasks_file.def'];

            taskdefname ='temp_RemoveOldFile';
            filepattern ='[FilePattern]';
            filterinfo  = struct('DateTimeRange', '-100000 [FileAge]');
            daemon.taskdef.TaskDef_Admin.RemoveFile(tasksdeffile, taskdefname, filepattern,filterinfo);

            ageindays=[];             taskdefname ='RemoveTasks';
            daemon.taskdef.TaskDef_Admin.RemoveTasks(tasksdeffile, taskdefname, ageindays);

            ageindays=1;             taskdefname ='RemoveTasksOlderThanOneDay';
            daemon.taskdef.TaskDef_Admin.RemoveTasks(tasksdeffile, taskdefname, ageindays, varargin);
            RecruringPeriod=1;             RecruringPeriodUnit='hour';
            taskfilename=[taskdefname '/EVERYONE.tsk'];
            daemon.taskdef.TaskDef_Admin.Scheduled(tempfolder, taskdefname, RecruringPeriod, RecruringPeriodUnit,taskfilename);

            ageindays=0.05;             taskdefname ='RemoveTasksOlderThanOneHour';
            daemon.taskdef.TaskDef_Admin.RemoveTasks(tasksdeffile, taskdefname, ageindays, varargin);
            RecruringPeriod=1;             RecruringPeriodUnit='minute';
            taskfilename=[taskdefname '/EVERYONE.tsk'];
            daemon.taskdef.TaskDef_Admin.Scheduled(tempfolder, taskdefname, RecruringPeriod, RecruringPeriodUnit,taskfilename);       
        end

        function Factory_Schedules(rootfolder,  varargin)
            tempfolder = [rootfolder 'template\ServiceAdmin\'];
            RecruringPeriod=1;             RecruringPeriodUnit='minute';
            taskdefname = 'CheckStatus';
            taskfilename=[taskdefname '/EVERYONE.tsk'];
            daemon.taskdef.TaskDef_Admin.Scheduled(tempfolder, taskdefname, RecruringPeriod, RecruringPeriodUnit,taskfilename);

            RecruringPeriod=1;             RecruringPeriodUnit='minute';
            taskdefname = 'ListFile_TasksInQueue';
            taskfilename=[taskdefname '/EVERYONE.tsk'];
            daemon.taskdef.TaskDef_Admin.Scheduled(tempfolder, taskdefname, RecruringPeriod, RecruringPeriodUnit,taskfilename);

            RecruringPeriod=1;             RecruringPeriodUnit='hour';
            taskdefname = 'RemoveTasksOlderThanOneDay';
            taskfilename=[taskdefname '/EVERYONE.tsk'];
            daemon.taskdef.TaskDef_Admin.Scheduled(tempfolder, taskdefname, RecruringPeriod, RecruringPeriodUnit,taskfilename);

            RecruringPeriod=1;             RecruringPeriodUnit='minute';
            taskdefname = 'RemoveTasksOlderThanOneHour';
            taskfilename=[taskdefname '/EVERYONE.tsk'];
            daemon.taskdef.TaskDef_Admin.Scheduled(tempfolder, taskdefname, RecruringPeriod, RecruringPeriodUnit,taskfilename);

            RecruringPeriod=5;             RecruringPeriodUnit='minute';
            taskdefname = 'ImportFocal';
            taskfilename=['../Focal/Focal_ImportPatients_1hour/EVERYONE.tsk'];
            daemon.taskdef.TaskDef_Admin.Scheduled(tempfolder, taskdefname, RecruringPeriod, RecruringPeriodUnit,taskfilename);

            RecruringPeriod=1;             RecruringPeriodUnit='hour';
            taskdefname = 'ImportFocal';
            taskfilename=['../Focal/Focal_ImportPatients_1hour/EVERYONE.tsk'];
            daemon.taskdef.TaskDef_Admin.Scheduled(tempfolder, taskdefname, RecruringPeriod, RecruringPeriodUnit,taskfilename);
        end


        function ExportSubDB(tasksdeffile,taskdefname, srcdbfile, dstdbfile, exportfilter, dstxlsfile)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj = daemon.taskdef.TaskDef_Admin({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
            process = struct('OperationType', 'ExportSubDB', 'SrcDBFile', srcdbfile, 'DstDBFile', dstdbfile, 'ExportFilter', exportfilter, 'DstXlsFile', dstxlsfile, 'CopySrcDBToLocal', '[CopySrcDBToLocal]', 'SrcDBTableNames', '[SrcDBTableNames]');
            DefaultSettings = struct('SrcDBFile', '','DstXlsFile', '', 'CopySrcDBToLocal', 1, 'SrcDBTableNames', '');
            obj.Generate(tasksdeffile, [], process, {'IsAdminTask', 1}, {'IsCustomTask', 1}, {'DefaultSettings', DefaultSettings});
        end

        function Factory_DcmDB(tasksdeffile)
            daemon.taskdef.TaskDef_Admin.ExportSubDB(tasksdeffile,'temp_ExportSubDB', '[SrcDBFile]', '[DstDBFile]', '[ExportFilter]', '[DstXlsFile]');
            daemon.taskdef.TaskDef_Admin.ExportSubDB(tasksdeffile,'temp_ExportSubDBFromMasterDB',    'C:/ARTDaemon/MasterDB/DataBase/plandb/RTPlanDB.sqlite', '[DstDBFile]', '[ExportFilter]', '[DstXlsFile]');
        end
    end
end