classdef DcmRTServer <OptionsWithLog
    properties
        m_DB;
        m_TaskDefs; 
    end
    
    methods
        function obj = DcmRTServer(varargin)
            options  = OptionsMap(); 
            options.setOption('TaskFileExts', {'*.tsk', '*.tsks', '*_tsk.xlsx', '*.tskinfo'});
            options.setOption('TaskStatusLockFile', 'status.lock');
            options.setOption('AutoDefaultTask', 1);
            options.setOption('server.tasksdef.file', 'tasks.def');
            options.setOption('server.rootfolder', './'); 
            options.setOption('FileReadyTimeInSecond', 3);
            options.setOption('StatusErrorRecoveryTrials', 2);
            curdir = pwd; 
            computername = DosUtil.ComputerName; 
            options.setOption('wtkapp.scriptroot', DosUtil.SimplifyPath([curdir '/test.' computername '/wtkapp/'])); 
            options.setOption('nnunet.scriptroot', DosUtil.SimplifyPath([curdir '/test.' computername '/nnUNETSeg/'])); 
            options.setOption('unetseg.scriptroot',DosUtil.SimplifyPath([curdir '/test.' computername '/UNETSeg/'])); 
            options.setOption('totalsegmentator_v2.scriptroot',DosUtil.SimplifyPath([curdir '/test.' computername '/totalsegmentator_v2/'])); 
            
            obj@OptionsWithLog('DcmRTServer', options, varargin{:});
            %obj.setUndefinedOption('log.file', [obj.LogFolder() obj.ServerName '.log']); 
            %computername = DosUtil.ComputerName; 
            obj.setUndefinedOption('log.file', [obj.LogFolder() computername '_' obj.ServerName  '.log']); 
            obj.m_DB = mydb.sqlite.DcmRTDB(obj.MyDBFile);
            
            LoadTasksDef(obj);
        end
        
        function LoadTasksDef(obj)
            % taskdeffile = obj.getoptioni('server.tasksdef.file');
            % if ~isempty(taskdeffile)
            %     taskdeffile = FullFile(obj, taskdeffile);
            % end
            % if exist(taskdeffile, 'file')==2
            %    obj.m_TaskDefs = utils.json.TasksDef(taskdeffile); 
            % end
            
            taskdeffiles = obj.getoptioni('server.tasksdef.file');
            if ischar(taskdeffiles)
                taskdeffiles=strsplit(taskdeffiles, '|');
            end

            for k=1:numel(taskdeffiles)
                taskdeffile=taskdeffiles{k};
                if ~isempty(taskdeffile)
                    taskdeffile = FullFile(obj, taskdeffile);
                end
                if exist(taskdeffile, 'file')~=2
                    continue;
                end
                if isempty(obj.m_TaskDefs )
                    obj.m_TaskDefs = utils.json.TasksDef(taskdeffile); 
                else
                    taskdef = utils.json.readJson(taskdeffile); 
                    fns = fieldnames(taskdef);
                    for m=1:numel(fns)
                        name = fns{m}; val = taskdef.(name);
                        obj.m_TaskDefs.SetConfig(name, val);
                    end
                end
            end

            tasks      = RegisteredTask(obj);
            taskfolder = TaskFolder(obj);
            for k=1:numel(tasks)
                DosUtil.mksubdir(taskfolder, tasks{k}); 
            end
        end
        
        function  taskdeffiles = TasksDefFile(obj)
            % taskdeffile = obj.getoptioni('server.tasksdef.file');
            % if ~isempty(taskdeffile)
            %     taskdeffile = FullFile(obj, taskdeffile);
            % end
            taskdeffiles = obj.getoptioni('server.tasksdef.file');
            if ischar(taskdeffiles)
                taskdeffiles=strsplit(taskdeffiles, '|');
            end
            taskdeffiles = cellfun(@(x)(FullFile(obj, x)), taskdeffiles, 'uniformoutput', false); 
            % I = cellfun(@(x)(exist(x, 'file')~=2), taskdeffiles); 
            % taskdeffiles(I)=[];
        end
        
        function  folder = TasksDefFolder(obj)
            %folder = [fileparts(TasksDefFile(obj)) '\'];
            taskdeffiles = TasksDefFile(obj);
            folder = [fileparts(taskdeffiles{1}) '\'];
        end
        
        function folder = RootFolder(self)
            folder = self.getoptioni('server.rootfolder');
            if ~DosUtil.isabsolutepath(folder)
                % curfolder = pwd; 
                % if ~DosUtil.isnetworkpath(curfolder)
                %     folder = [curfolder '/' folder];
                %     folder = strrep(folder, '\', '/');
                % else
                %     folder = [curfolder '\' folder];
                %     folder = strrep(folder, '/', '\');
                % end
                % folder = DosUtil.SimplifyPath(folder);
                folder = DosUtil.SimplifyPath([pwd '/' folder]);
                self.setOption('server.rootfolder', folder);
            end
        end
        
        function fname = FullFile(self, fname)
            if ~DosUtil.isabsolutepath(fname)
                fname = [RootFolder(self) fname];
            end
            % if ~DosUtil.isnetworkpath(fname)
            %     fname = strrep(fname, '\', '/');
            % end
            fname = DosUtil.SimplifyPath(fname);
        end
        
        function res = OutputRootFolder(self)
            res = self.getoptioni('output.rootfolder'); 
            if ~DosUtil.isabsolutepath(res)
                res = [RootFolder(self) res];
                %res = strrep(res, '\', '/');
                res = DosUtil.SimplifyPath(res); 
                self.setOption('output.rootfolder', res); 
            end
        end
        
        function flag = AutoDefaultTask(self)
            flag = self.getoptioni_numeric('AutoDefaultTask');
        end
        
        function res = ServerName(self)
            res = self.getoptioni('ServerName');
        end
        
        function res = DcmDataFolder(self)
            res = self.getoptioni('DCMSERVER_FOLDER',   [self.RootFolder() '../DataBase/DicomData/']);
            if ~DosUtil.isabsolutepath(res)
                res = self.toFullFile(res, [], [pwd '\']); 
                self.setOption('DCMSERVER_FOLDER', res); 
            end
        end
        
        function res = DcmServerTasksFolder(self)
            res = self.getoptioni('DCMSERVER_TASKS_FOLDER',   [self.RootFolder() '../DataBase/tasks/']);
            res = DosUtil.SimplifyPath(res); 
        end
        
        function res = DcmPortsTableFile(self)
            str = 'DCM_PORTS_TABLE_FILE';
            if self.isOption(str)
                res = self.getOption(str); 
            else
                res = DosUtil.SimplifyPath([self.RootFolder() '../ServiceDcmServer/DCMPorts.xlsx']);
                self.setOption(str, res); 
            end
        end
        
        function T = DcmPortsTable(self)
            str = 'DCM_PORTS_TABLE';
            if self.isOption(str)
                T = self.getOption(str); 
            else
                fname = DcmPortsTableFile(self);
                T = xls.TableBase.table_cellstr(fname);
                %T.AETitle = upper(T.AETitle);
                T.Properties.RowNames=T.('DcmPortName');
                self.setOption(str, T); 
            end
        end
        
        function res = MyDBFile(self)
            res = self.getoptioni('DBFileName');
        end
        
        function res = MyDB(self)
            res = self.m_DB;
        end
        
        function res = TasksRootFolder(self)
            str = 'tasks.rootfolder'; 
            res = self.getoptioni(str);
            if isempty(res)
                res = DosUtil.mksubdir(OutputRootFolder(self), 'Tasks');     
                self.setOption(str, res); 
            end
        end
        
        function res = TasksHistoryRootFolder(self)
            str = 'taskshistory.rootfolder'; 
            res = self.getoptioni(str);
            if isempty(res)
                res = DosUtil.mksubdir(OutputRootFolder(self), 'TasksHistory');     
                self.setOption(str, res); 
            end
        end

        function res = LogFolder(self)
            str = 'server.logfolder'; 
            res = self.getoptioni(str);
            if isempty(res)
                res = DosUtil.mksubdir(OutputRootFolder(self), 'log');     
                self.setOption(str, res); 
            end
        end
        
        function res = TaskFolder(self)
            str = [ServerName(self) '.TaskFolder'];
            res = self.getOption(str);
            if isempty(res)
                res = DosUtil.mksubdir(TasksRootFolder(self), ServerName(self));
                self.setOption(str, res);
            end
        end
        
        function res = HistoryFolder(self)
            %res = DosUtil.mksubdir(TasksHistoryRootFolder(self),  ServerName(self));
            str = [ServerName(self) '.HistoryFolder'];
            res = self.getOption(str);
            if isempty(res)
                res = DosUtil.mksubdir(TasksHistoryRootFolder(self),  ServerName(self));
                self.setOption(str, res);
            end
        end
        
        function res = PersistentTaskFile(self)
            defaultfile = [RootFolder(self) 'tasks.persistent/persistenttasks.json'];
            res = self.getoptioni('PersistentTaskFile', defaultfile);
            if ~DosUtil.isabsolutepath(res)
                res = [RootFolder(self) res];
                res = strrep(res, '\', '/');
                self.setOption('PersistentTaskFile', res); 
            end
        end

        function res = RegisteredTask(self)
            if ~self.isoptioni('registered.task')
                res = self.m_TaskDefs.FullTaskNames();
            else
                res = self.getoptioni('registered.task');
            end
            
            if ischar(res)
                res = strsplit(res, '|');
            end
        end
                
        function res = TaskFileExts(self)
            res = self.getoptioni('TaskFileExts'); 
            if ischar(res)
                res = strsplit(res, '|');
            end
        end
        
        function RunServer(self, servermode)
            if ~exist('servermode', 'var')
                servermode = 1;
            end
                       
            %registeredtasks = self.RegisteredTask; 
            
            self.LogInfo(['*********************************Start ' self.ServerName '***********************']);
            
            while(1)
                if AutoDefaultTask(self)
                    ProcessPersistentTasks(self);
                end
                
                ProcessTaskFolder(self, TaskFolder(self), 0);
                
                if ~servermode
                    break;
                end
                
                if (~isdeployed)
                    disp(['waitfor new task.....']); 
                end
                pause(3);
            end
        end
        
                               
        %to be implement by subclass
        %status=0, task not ready or locked, 1: task processed, 2: err
        function status = ProcessTask(self, task, info, varargin)
            %taskname = task.getfieldx('TaskName');
            if iscell(info)
                info1 = info{1};
            else
                info1 = info; 
            end
            
            task.WildCardRepStr(info);
            statusfolder = TaskOutputFolder(self, task, info1);
            conditions = GET_TASK_CONFIG(task,'Conditions', []); 
            if ~isempty(conditions) && ~self.CheckTaskCondition(conditions, info, statusfolder)
                status = 1; 
                return;
            end

           
            iscustomtask = GET_TASK_CONFIG(task,'IsCustomTask', 0); 
%             if iscustomtask
%                 if exist(statusfolder, 'dir')==7
%                     delete(statusfolder); 
%                 end
%             end
            
            if ~iscustomtask
                taskdoneindicator  = GET_TASK_CONFIG(task,'TaskDoneIndicator', ''); 
                if ischar(taskdoneindicator)  
                    if exist([statusfolder taskdoneindicator], 'file')==2
                        status = 1; 
                        return; 
                    end
                else
                    taskdoneindicator=DosUtil.WildCardRepStr_recursive(taskdoneindicator, info); 
                    [flag] = DosUtil.CheckFileOrFolder(taskdoneindicator);
                    if flag
                        status = 1; 
                        return; 
                    end
                end
            end
          
            CalcPreTasks(self,  task, info);
            
            [taskready, info] = ParseTaskExtraSource(self, task, info);
            if ~taskready
                status =0; return; 
            end

            dependency = GET_TASK_CONFIG(task,'Dependency');
            if ~isempty(dependency)
                if isstruct(dependency)
                    dependency = arrayfun(@(x)(x), dependency, 'uniformoutput', false); 
                end
                taskready = 1; 
                for k=1:numel(dependency)
                    depstr    = dependency{k};
                    readyfname = StructBase.getfieldx(depstr, 'filename');
                    if isempty(readyfname)
                        continue;
                    end
                    [depready, IsBusy] = utils.json.TasksDef.IsTaskReady(readyfname, info, statusfolder, 0);
                    if ~depready 
                        %status = 0; 
                        if ~IsBusy
                        taskfilename = StructBase.getfieldx(depstr, 'taskfilename');
                        if ~isempty(taskfilename)
                            tempname = utils.json.TasksDef.WildCardRepStr(taskfilename, info);
                            %fname = DosUtil.SimplifyPath([TaskFolder(self) tempname]);
                            fname = DosUtil.CatenatePath(tempname,  TaskFolder(self));
                            if ~exist(fname, 'file')
                                fclose(fopen(fname, 'w'));
                            end
                        end
                        TaskInfo=StructBase.getfieldx(depstr,"TaskInfo");
                        if ~isempty(TaskInfo)
                            self.WriteInfo2Json(TaskInfo, TaskFolder(self));
                        end
                        end
                    else %use this as signal for slot function
                        taskfilename = StructBase.getfieldx(depstr, 'slotfilename');
                        if ~isempty(taskfilename)
                            tempname = utils.json.TasksDef.WildCardRepStr(taskfilename, info);
                            fname = [TaskFolder(self) tempname];
                            if ~exist(fname, 'file')
                                fclose(fopen(fname, 'w'));
                            end
                        end 
                    end
                    taskready = taskready & depready; 
                end
                
                if ~taskready
                    status =0; return; 
                end
            end
       
            ageThreshInsecond = self.getoptioni_numeric('FileReadyTimeInSecond'); 
            readyfname = GET_TASK_CONFIG(task,'TaskReadyIndicator', ''); 
            if ~isempty(readyfname) 
                if ischar(readyfname)
                    ready = utils.json.TasksDef.IsTaskReady(readyfname, info, statusfolder, ageThreshInsecond);
                    if ~ready
                        status =0; 
                        return; 
                    end
                else
                    readyfname=DosUtil.WildCardRepStr_recursive(readyfname, info); 
                    [ready] = DosUtil.CheckFileOrFolder(readyfname);
                    if ~ready
                        status = 0; 
                        return; 
                    end
                end
            end
            
           
            
            statuslockfile       = [statusfolder 'status.lock']; 
            if ~iscustomtask && exist(statuslockfile, 'file')==2 
                status = 0; 
                return;
            end
            
            statuserrfile        = [statusfolder 'status.err'];
            if ~iscustomtask && exist(statuserrfile, 'file')==2
                trials = self.getoptioni_numeric('StatusErrorRecoveryTrials', 0);
                if trials>0
                    for k=1:trials
                        statuserrfile1 = [statuserrfile '.trial' num2str(k)];
                        if ~exist(statuserrfile1, 'file')
                            movefile( statuserrfile,  statuserrfile1); status=0; return;
                        end
                    end
                end
                status = 2; 
                return;
            end

            try
                fclose(fopen(statuslockfile, 'w'));
                self.LogInfo(statuslockfile);  
                
                CalcMiscProcesses(self,  task, info, 'Process');

                CalcTask(self, task, info, varargin{:}); 
                
                try
                CalcDcmExportFile(self,  task, info);
                catch
                end
                
                CalcPostTaskEval(self,  task, info);
                
                CalcPostTaskRecord(self,  task, info);

                %task.writeJson([statusfolder 'task.cfg']); 
                taskcfgoutput = GET_TASK_CONFIG(task,'TaskConfigOutput', 'task.cfg'); 
                if ~isempty(taskcfgoutput)
                    task.writeJson([statusfolder taskcfgoutput]); 
                end
                
                delete(statuslockfile);
                status = 1; 
                
                CalcPostTasks(self,  task, info);

                archive = GET_TASK_CONFIG(task,'WatchDog');
                if ~isempty(archive)
                    self.WatchDog(archive, info);
                end
                
            catch err
                delete(statuslockfile);
                status = 2; 
                self.LogErr(err)    
                report = getReport(err, 'extended');
                self.UpdateStatusFile(statusfolder,'err', report);
                posttasks = GET_TASK_CONFIG(task,'ExceptionHandeling');
                if ~isempty(posttasks)
                    if isstruct(posttasks)
                        posttasks = arrayfun(@(x)(x), posttasks, 'uniformoutput', false); 
                    end
                    for k=1:numel(posttasks)
                        posttaskstr    = posttasks{k};
                        taskfilename = StructBase.getfieldx(posttaskstr, 'taskfilename');
                        if ~isempty(taskfilename)
                            tempname = utils.json.TasksDef.WildCardRepStr(taskfilename, info);
                            fname = [TaskFolder(self) tempname];
                            if ~exist(fname, 'file')
                                fclose(fopen(fname, 'w'));
                            end
                        end
                    end
               end
            end
        end
        
        function CalcPreTasks(self,  task, info)
            CalcMiscProcesses(self,  task, info, 'PreTask');
        end

        function CalcMiscProcesses(self,  task, info, configname)
            posttasks = GET_TASK_CONFIG(task,configname);
            if ~isempty(posttasks)
                posttasks = StructBase.toCell(posttasks);
                statusfolder = TaskOutputFolder(self, task, info);
                for k=1:numel(posttasks)
                    posttaskstr    = posttasks{k};
                    MiscProcess(self, posttaskstr, info, statusfolder);
                end
            end
        end

        function CalcPostTasks(self,  task, info)
            try
            CalcPostTaskSignal(self,  task, info);
            catch
            end
            posttasks = GET_TASK_CONFIG(task,'PostTask');
            if ~isempty(posttasks)
                statusfolder = TaskOutputFolder(self, task, info);
                if isstruct(posttasks)
                    posttasks = arrayfun(@(x)(x), posttasks, 'uniformoutput', false); 
                end
                for k=1:numel(posttasks)
                    posttaskstr    = posttasks{k};
                    MiscProcess(self, posttaskstr, info, statusfolder);
                end
            end
        end

        function MiscProcess(self, posttaskstr, info, statusfolder)
    %%%%%%%%%%%%%%%%11/06/2024 Add Condition%%%%%%%%%%%%%%%%%%%%%%%%%%
            conditions = StructBase.getfieldx(posttaskstr, 'Condition');
            if ~isempty(conditions)
                if ~self.CheckTaskCondition(conditions, info, statusfolder)
                    return;
                end
            end
            
            signalfilename = StructBase.getfieldx(posttaskstr, 'signalfilename');
            if ~isempty(signalfilename)
                signalfile = self.toFullFile(signalfilename, info,statusfolder);
                if ~exist(signalfile, 'file')
                    return; 
                end
            end
            optype = StructBase.getfieldx_default(posttaskstr, 'OperationType', ''); 
            xoptype = StructBase.getfieldx_default(posttaskstr, 'x_OperationType', ''); %OperationType commented
            if ~isempty(optype)
                MiscProcess_admin(self, optype, posttaskstr, info, statusfolder);

                MiscProcess_common(self, optype, posttaskstr, info, statusfolder);

                MiscProcess_dcm(self,optype, posttaskstr, info, statusfolder);

                MiscProcess_sub(self,optype,  posttaskstr, info, statusfolder);

            elseif isempty(xoptype)
                MiscProcess_legacy(self, posttaskstr, info, statusfolder);
            end
        end
        
        function MiscProcess_dcm(self,optype, taskstr, info, statusfolder)
             switch optype
                case {'ImportDcmPatient'}    
                    self.ImportDcmPatient(taskstr, info);

                case {'ExportDcm'}
                    str = taskstr; str.RootFolder = statusfolder; 
                    self.DcmExport(str, info);

                 case {'ExportSubDB'}
                    % subdbfile = 'C:\temp\RTPlanDB20250624_sub.sqlite';
                    % task = struct('DstDBFile', subdbfile, 'DstXlsFile', strrep(subdbfile, '.sqlite', '.xlsx'), ...
                    %     'ExportFilter', filterT);
                    self.ExportSubDB(taskstr, info, statusfolder);

                case {'VolImage2Dcm'}    
                    %self.VolImage2Dcm(taskstr, info);
                    img = StructBase.getfieldx(taskstr, 'VolImage');
                    if ischar(img)
                        img = DosUtil.CatenatePath(img, statusfolder);
                    end
                    str = taskstr; str.RootFolder=statusfolder; 
                    outfolder = dicom.utils.VhImage2Dcm.SaveImage2Dcm(img, str);
                    outdcmvhfile = StructBase.getfieldx(taskstr, 'OutputDcmVhFile');
                    if ~isempty(outdcmvhfile)
                        outdcmvhfile = DosUtil.CatenatePath(outdcmvhfile, statusfolder);
                        [vhct, dcmvh] = VolHeaderImage.LoadDcmVol([outfolder '*.dcm']);
                        dcmvh.writeJsonHeader(outdcmvhfile);
                    end

                case {'DcmExportROIMask'}
                    roimaskimg=StructBase.getfieldx(taskstr, 'InputROIMaskImage');
                    if ischar(roimaskimg)
                        fname = DosUtil.CatenatePath(roimaskimg, statusfolder);
                        roimaskimg=ROIMaskImage(fname);
                    end
                    outdicomrsfile=StructBase.getfieldx(taskstr, 'OutputDcmRSFile');
                    %outdicomrsfile=DosUtil.CatenatePath(outdicomrsfile, statusfolder);
                    RSSetName  =StructBase.getfieldx_default(taskstr, 'RSSetName', 'SegmanRS');
                    self.DcmExportROIMask(taskstr, roimaskimg, statusfolder, outdicomrsfile,  RSSetName); 
             end
        end

        function MiscProcess_common(self,optype, taskstr, info, statusfolder)
            switch optype
                % case 'CopyFile'
                %     self.CopyFile(taskstr, info, statusfolder);
                % case 'CopyFiles'
                %     DosUtil.WatchDog.CopyFiles(taskstr, info);
                case 'AssociateDoc'
                    self.AssociateDoc(taskstr,info, statusfolder);
                case 'AssociateStruct'
                    self.AssociateStruct2List(taskstr,info, statusfolder);
                % case 'ExecuteCommand'
                %     self.ExecuteCommand(taskstr,info, statusfolder);
                case 'CreateTaskFile'
                    taskfilename = StructBase.getfieldx(taskstr, 'TaskFileName');
                    self.CreateTaskFile(taskfilename, info);
                case {'CreateTskInfo'}
                    self.WriteInfo2Json(taskstr, self.TaskFolder);
                case {'WriteInfo2Json'}
                    self.WriteInfo2Json(taskstr, statusfolder);
                case {'nnUNETInference'}
                    self.nnUNETInference(taskstr, statusfolder, self);
                case {'TotalSeg'}
                    self.TotalSeg(taskstr, statusfolder, self);
                case {'ROIEval'}
                    self.ROIEval(taskstr,  statusfolder, info);    
                case {'ROIMask2StructSet'}
                    self.ROIMask2StructSet(taskstr, statusfolder, info);

                case {'MergeExportROIMask'}
                    str = taskstr; str.TaskOutputFolder = statusfolder; 
                    self.MergeExportROIMask(str, info);

                case {'RegMapImage'}
                    str = taskstr; str.RootFolder=statusfolder; 
                    self.RegMapImage(str, info);

                case {'ProcessROIMask'}
                    self.ProcessROIMask(taskstr, info, statusfolder);
                 
                case {'ProcessImage'}
                    self.ProcessImage(taskstr, info, statusfolder);
                
           end
        end
        
        function MiscProcess_admin(self,optype, taskstr, info, statusfolder)
            switch optype
                case 'CopyFile'
                    self.CopyFile(taskstr, info, statusfolder);
                case 'CopyFiles'
                    DosUtil.WatchDog.CopyFiles(taskstr, info);
                case 'ExecuteCommand'
                    self.ExecuteCommand(taskstr,info, statusfolder);
                case 'RemoveFile'
                    DosUtil.RemoveFile(taskstr, statusfolder);
                case 'RemoveFolder'
                    DosUtil.RemoveFolder(taskstr, statusfolder);    
                case 'ListSubFolders'
                    self.ListSubFolders(taskstr, statusfolder);
                case 'ImportDcmFolder'
                    self.ImportDcmFolder(taskstr, statusfolder);
            end
        end

        %to be implemented by subclass
        function MiscProcess_sub(self,optype, taskstr, info, statusfolder)
        
        end

        %%the LegacyMiscProcess is for past compatibility, it will be removed later on 
        function MiscProcess_legacy(self, posttaskstr, info, statusfolder)
            taskfilename = StructBase.getfieldx(posttaskstr, 'taskfilename');
            if ~isempty(taskfilename)
                tempname = utils.json.TasksDef.WildCardRepStr(taskfilename, info);
                fname = [TaskFolder(self) tempname];
                [~, ~, ext] = fileparts(fname); 
                if strcmpi(ext, '.cmd')||strcmpi(ext, '.bat')
                    system(fname); 
                else
                    if ~exist(fname, 'file')
                        fclose(fopen(fname, 'w'));
                    end
                end
            end

            CopyFileStr = StructBase.getfieldx(posttaskstr, 'CopyFile');
            if ~isempty(CopyFileStr)
                self.CopyFile(CopyFileStr, info, statusfolder);
            end
            
            CopyFileStr = StructBase.getfieldx(posttaskstr, 'CopyFiles');
            if ~isempty(CopyFileStr)
                DosUtil.WatchDog.CopyFiles(CopyFileStr, info);
            end

            AssociateDocStr = StructBase.getfieldx(posttaskstr, 'AssociateDoc');
            if ~isempty(AssociateDocStr)
                self.AssociateDoc(AssociateDocStr,info, statusfolder);
            end
            
            AssociateStr = StructBase.getfieldx(posttaskstr, 'AssociateStruct');
            if ~isempty(AssociateStr)
                self.AssociateStruct2List(AssociateStr,info, statusfolder);
            end

            ExecuteCommandStr = StructBase.getfieldx(posttaskstr, 'ExecuteCommand');
            if ~isempty(ExecuteCommandStr)
                self.ExecuteCommand(ExecuteCommandStr,info, statusfolder);
            end
        end
        
        function status = ProcessAdminTask(self, task, info, varargin)
            try
                
                CalcMiscProcesses(self,  task, info, 'Process');

                status = CalcAdminTask(self, task, info, varargin{:});
                CalcPostTasks(self,  task, info);

                archive = GET_TASK_CONFIG(task,'Archive');
                if ~isempty(archive)
                    self.ArchiveFolder(archive, info);
                end
            
                archive = GET_TASK_CONFIG(task,'WatchDog');
                if ~isempty(archive)
                    self.WatchDog(archive, info);
                end

            catch err
                status = 2;
                self.LogErr(err);
            end
        end
        
        function status = CalcAdminTask(self, task, info, varargin)
            taskname = upper(task.getfieldx('TaskName')); 
            isadmintask = GET_TASK_CONFIG(task,'IsAdminTask');
            if isadmintask <2
                self.LogInfo(taskname); 
            end
            if regexpi(taskname, '^RemoveFile', 'once')
                CalcTask_RemoveFile(self, task, info);
            elseif regexpi(taskname, '^RemoveFolder', 'once')
                CalcTask_RemoveFolder(self, task, info);
            elseif regexpi(taskname, '^ListFile', 'once')
                CalcTask_ListFile(self, task, info);
            elseif regexpi(taskname, '^QueryDBTable', 'once')
                CalcTask_QueryDBTable(self, task, info);
            elseif regexpi(taskname, '^ScheduleTask', 'once')
                CalcTask_ScheduleAdminTask(self, task, info);  
            elseif regexpi(taskname, '^Archive', 'once')
                CalcTask_Archive(self, task, info);    
            end
            status = 1; 
        end
        
        function CalcTask_ScheduleTask(self, task, info)
           CalcTask_ScheduleAdminTask(self, task, info);
        end
        
        function [taskready, info] = ParseTaskExtraSource(self, task, info)
            taskready   = true; 
            extrasource = GET_TASK_CONFIG(task,'ExtraSource');
            if isempty(extrasource)
                return;
            end
            
            filters = StructBase.getfieldx(extrasource, 'Filter');
            rtfilter = xls.RTTableFilter(); 
            if ischar(filters)
                %filterfilename = filters; 
                fname = self.m_TaskDefs.FullFile(filters); 
                rtfilter.LoadFilter_json(fname); 
            else
                rtfilter.AddTableFilter(filters);
            end

            rtfilter.ConvertMatchInfo(info);
            %[I, T] = rtfilter.FilterTable(dbT);
            dbname  = StructBase.getfieldx(extrasource, 'DBTableName');
            T    = self.m_DB.GetTable_filter(dbname, rtfilter);
            if ~isempty(T)
                info1 = table2struct(T(1, :));
                if isstruct(info)
                    info = {info, info1};
                elseif iscell(info)
                    K    = numel(info);
                    info{K+1}=info1;
                end
                task.WildCardRepStr(info);
            else
                taskready = false;
            end
        end
        
        function CalcTask_ScheduleAdminTask(self, task, info)
            posttasks = GET_TASK_CONFIG(task,'ScheduleTask');
            CreateTaskFiles(self, posttasks, info);
        end

        function CreateTaskFiles(self, posttasks, info)
            if ~isempty(posttasks)
                if isstruct(posttasks)
                    posttasks = arrayfun(@(x)(x), posttasks, 'uniformoutput', false); 
                end
                for k=1:numel(posttasks)
                    posttaskstr    = posttasks{k};
                    taskfilename = StructBase.getfieldx(posttaskstr, 'taskfilename');
                    if ~isempty(taskfilename)
                        tempname = utils.json.TasksDef.WildCardRepStr(taskfilename, info);
                        fname = [TaskFolder(self) tempname];
                        if ~exist(fname, 'file')
                            fclose(fopen(fname, 'w'));
                        end
                    end
                    CopyFileStr = StructBase.getfieldx(posttaskstr, 'CopyFile');
                    if ~isempty(CopyFileStr)
                        self.CopyFile(CopyFileStr, info, statusfolder);
                    end
                end
            end
        end
        
        function CalcTask_QueryDBTable(self, task, info)
            taskid = GET_TASK_CONFIG(task,'TaskUID'); 
            taskid = strrep(taskid, '#', '/');
            dbname = GET_TASK_CONFIG(task,'DBTable'); 
            querystr= GET_TASK_CONFIG(task,'QueryStr', ''); 
            T = self.m_DB.QueryDBTable(dbname, querystr);
            filter = GET_TASK_CONFIG(task,'DBTableFilter'); 
            if ~isempty(filter)
                dbfilter = xls.TableFilter; 
                dbfilter.AddTableFilter(filter);
                [~, T] = dbfilter.FilterTable(T); 
            end
            outxlsfile = GET_TASK_CONFIG(task,'OutputXlsFile');
            if isempty(outxlsfile)
                timestamp = datestr(datetime, 'yyyymmdd');
                outxlsfile = ['../DataBase/Excel/RTPlanDB_' timestamp '.xlsx'];
            end

            if ~DosUtil.isabsolutepath(outxlsfile)
                outxlsfile =DosUtil.SimplifyPath([OutputRootFolder(self) outxlsfile]);
            end

            if strcmpi(dbname, 'RTSTRUCT')
                RetrieveROINames=GET_TASK_CONFIG(task,'RetrieveROINames', false);
                if RetrieveROINames
                    T  = ai.DcmParser.RSTable2StructureNames(T, DcmDataFolder(self));
                end
            end

            [outfolder]=fileparts(outxlsfile); DosUtil.mksubdir(outfolder);
            outsheetname = GET_TASK_CONFIG(task,'OutputSheetName', dbname);
            xls.TableBase.WriteTable(T, outxlsfile, 'sheet', outsheetname);
        end

         function CalcTask_ListFile(self, task, info)
             rootfolder = GET_TASK_CONFIG(task,'RootFolder');
             if ~DosUtil.isabsolutepath(rootfolder) 
                 rootfolder = [OutputRootFolder(self) rootfolder];
             end
             rootfolder = DosUtil.SimplifyPath(rootfolder); 
             filepat = GET_TASK_CONFIG(task,'FilePattern');
             res     = DosUtil.rdir([rootfolder, filepat]); 
             fnames  = {res(:).name};
             fnames  = cellfun(@(x)(DosUtil.relativepath(x, rootfolder)), fnames, 'uniformoutput', false);
             outfilename = GET_TASK_CONFIG(task,'OutputJsonFile');
             utils.json.writeJson(fnames, outfilename);
         end
        
         function CalcTask_RemoveFile(self, task, info)
            taskid = GET_TASK_CONFIG(task,'TaskUID'); 
            taskid = strrep(taskid, '#', '/');
            names = GET_TASK_CONFIG(task,'RemoveFileName'); 
            if ischar(names)
                names = strsplit(names, '|'); 
            end
            %rootfolder =  OutputRootFolder(self);
            rootfolder = GET_TASK_CONFIG(task,'RootFolder');
            if isempty(rootfolder)
                rootfolder =  OutputRootFolder(self);
            end

            for k=1:numel(names)
                name = names{k}; 
                if strcmp(taskid, 'EVERYONE')
                    res = DosUtil.rdir([rootfolder  '/**/' name]);
                else
                    res = DosUtil.rdir([rootfolder taskid '/**/' name]);
                end
                if ~isempty(res)
                    fnames = {res(:).name};
                    cellfun(@(x)(delete(x)), fnames);
                end
            end
         end
        
         function CalcTask_RemoveFolder(self, task, info)
            taskid = GET_TASK_CONFIG(task,'TaskUID'); 
            taskid = strrep(taskid, '#', '/');
            names = GET_TASK_CONFIG(task,'RemoveFolderName'); 
            if ischar(names)
                names = strsplit(names, '|'); 
            end
            rootfolder =  OutputRootFolder(self);
            for k=1:numel(names)
                try
                name = names{k}; 
                if strcmp(name, '*')
                    rmdir([rootfolder taskid], 's'); 
                    continue;
                end
                
                res = DosUtil.rdir([rootfolder taskid '/**/' name]);
                res(~[res(:).isdir])=[];
                for m = 1:numel(res)
                    foldername = res(k).name; 
                    rmdir(foldername, 's'); 
                end
                catch
                end
            end
         end
         
        % function ProcessPreTasks(self, tasks, info)
        % 
        % end
         
        function status = CalcTask(self, task, info, varargin)
             status =1; 
        end
        
        function ProcessTaskList(self, task, xlsfile, varargin)
            [taskfolder, taskname, ext] = fileparts(xlsfile);
            infos=[];
            if strcmpi(ext, '.xlsx') || strcmpi(ext, '.csv')
                T=xls.TableBase.ReadTable(xlsfile);
                infos = table2struct(T);
            elseif strcmpi(ext, '.tskinfo')
                infos = utils.json.readJson(xlsfile);
            end

           infos = StructBase.toCell(infos);
            %N = size(T, 1); 
            N = numel(infos);
            for k=1:N
                %info  = table2struct(T(k, :));
                info=infos{k};
                taskuid= StructBase.getfieldx(info, 'TaskUID');
                if isempty(taskuid)
                    try
                        modality = upper(info.Modality);
                        switch modality
                            case {'CT', 'MR', 'PT'}
                                taskuid = [modality '.' info.SeriesInstanceUID]; 
                            case {'RTPLAN'}
                                taskuid = ['RP.' info.SOPInstanceUID]; 
                            case {'RTSTRUCT'}
                                taskuid = ['RS.' info.SOPInstanceUID]; 
                            case {'RTDOSE'}
                                taskuid = ['RD.' info.SOPInstanceUID]; 
                            case {'REG'}
                                taskuid = ['RE.' info.SOPInstanceUID]; 
                            otherwise
                                taskuid = [modality '.' info.SOPInstanceUID]; 
                        end
                    catch
                    end
                end
                
                if isempty(taskuid)
                    taskuid = [taskname '_' num2str(k)];
                end
                
                task1 = utils.json.JsonTask;
                task1.setfieldx('TaskUID', taskuid); 
                task1.m_cfg = DosUtil.WildCardRepStr_recursive(task.m_cfg, info);
                DefaultSettings = GET_TASK_CONFIG(task,'DefaultSettings');
                if ~isempty(DefaultSettings)
                    task1.m_cfg = DosUtil.WildCardRepStr_recursive(task1.m_cfg, DefaultSettings);
                end

                taskfile = [taskfolder '\' taskuid '.tsk'];
                task1.writeJson(taskfile);
                %ProcessMyTask(self, task, info, varargin{:});
            end
        end

        function ProcessTaskTable_db(self, task, T, varargin)
            N = size(T, 1); 
            for k=1:N
                info = table2struct(T(k, :));
                try
                modality = upper(info.Modality);
                switch modality
                    case {'CT', 'MR', 'PT'}
                        taskid = [modality '.' info.SeriesInstanceUID]; 
                    case {'RTPLAN'}
                        taskid = ['RP.' info.SOPInstanceUID]; 
                    case {'RTSTRUCT'}
                        taskid = ['RS.' info.SOPInstanceUID]; 
                    case {'RTDOSE'}
                        taskid = ['RD.' info.SOPInstanceUID]; 
                    case {'REG'}
                        taskid = ['RE.' info.SOPInstanceUID]; 
                    otherwise
                        taskid = [modality '.' info.SOPInstanceUID]; 
                end
                task.setfieldx('TaskUID', taskid); 
                catch 
                end
                ProcessMyTask(self, task, info, varargin{:});
            end
        end
        
        function ProcessPersistentTasks(self)
            fname   = self.PersistentTaskFile();
            if ~exist(fname, 'file')
               return
            end
            cfg   = utils.json.JsonConfig(fname);
            tasks = cfg.m_cfg;

            if iscell(tasks)
                taskfiles    = cellfun(@(x)(cfg.FullFile(StructBase.getfieldx(x, 'taskfile'))), tasks, 'uniformoutput', false, 'ErrorHandler', @(x, y)('')); 
                I = cellfun(@(x)(isempty(x)), taskfiles); 
                taskfiles(I)=[];
            else
                taskfiles    = arrayfun(@(x)(cfg.FullFile(StructBase.getfieldx(x, 'taskfile'))), tasks, 'uniformoutput', false, 'ErrorHandler', @(x, y)('')); 
                I = arrayfun(@(x)(isempty(x)), taskfiles); 
                taskfiles(I)=[];
            end
            ispersistent = 1; 
            ProcessTaskFiles(self, taskfiles, ispersistent);
        end
        
        function ProcessTaskFolder(self, taskfolder, ispersistent)
            exts     = TaskFileExts(self);
            for k=1:numel(exts)
                ext  = exts{k}; 
                res  = DosUtil.rdir([taskfolder '**/'  ext]); 
                taskfiles = {res(:).name};
                ProcessTaskFiles(self, taskfiles,   ispersistent);
            end
        end
        
        function ProcessTaskFiles(self, taskfiles, ispersistent)
            for k=1:numel(taskfiles)
                try
                    taskfile = taskfiles{k};
                    if isempty(taskfile)
                        continue;
                    end
                    [task, processed] = ProcessTaskFile(self, taskfile);
                    if ~ispersistent && processed
                        LogTask(self, task,  taskfile);
                    end
                catch err
                    self.LogErr(err);
                end
            end
        end
        
        function task = ParseTaskFile(self, taskfile)
            %taskfile  = strrep(taskfile, '\', '/');
            taskfile = DosUtil.SimplifyPath(taskfile);
            [takspath, name, ext] = fileparts(taskfile); 
            

            switch lower(ext)
                case {'.xlsx', '.tskinfo'}
                    task      = utils.json.JsonTask(); 
                    taskname  = task.getfieldx('TaskName');
                    if isempty(taskname) 
                        subs      = strsplit(takspath, '/');
                        names     = strsplit(subs{end}, '.'); 
                        task.setfieldx('TaskName', names{1});
                        if numel(names)==2
                            task.setfieldx('SubTaskName', names{2});
                        end
                    end
                case {'.tsks'}
                    task      = utils.json.JsonTask(taskfile); 
                    dbtablefilter= GetConfig(task, 'DBTableFilter');
                    if ~isempty(dbtablefilter)
                        filter = xls.TableFilter; 
                        if isstruct(dbtablefilter)
                            filter.setFilter(dbtablefilter);
                        elseif ischar(dbtablefilter) 
                            fname = task.FullFile(dbtablefilter); 
                            filter.LoadFilter_json(fname); 
                        end
                        task.setfieldx('DBTableFilter', filter); 
                    end
                    
                case {'.tsk'}
                    task      = utils.json.JsonTask(taskfile); 
                    taskname  = task.getfieldx('TaskName');
                    if isempty(taskname) 
                        % isnetworkpath = startsWith(takspath, '\\');
                        % if isnetworkpath
                        %     subs      = strsplit(takspath, '\');
                        % else
                            subs      = strsplit(takspath, '/');
                        % end
                        names     = strsplit(subs{end}, '.'); 
                        task.setfieldx('TaskName', names{1});
                        if numel(names)==2
                            task.setfieldx('SubTaskName', names{2});
                        end
                    end
                    uid = task.getfieldx('TaskUID');
                    if isempty(uid)
                        task.setfieldx('TaskUID', name); 
                    end
            end
        end
        
        function [task, processed] = ProcessTaskFile(self, taskfile)
            try
                processed= false; 
                task     = ParseTaskFile(self, taskfile); 
                taskname = GET_TASK_CONFIG(task,'TaskName','');
                subtaskname  = GET_TASK_CONFIG(task,'SubTaskName','');
                fulltaskname = taskname;
                if ~isempty(subtaskname)
                    fulltaskname = [taskname '.' subtaskname];
                end
                if ~ismember(fulltaskname, RegisteredTask(self))
                    return;
                end
                
                taskdef  = task.getfieldx('TaskDef');
                if isempty(taskdef)
                    taskdef  = self.m_TaskDefs.GetTaskDef(taskname);
                    if isempty(taskdef)
                        return; 
                    end
                    
                    task.setfieldx('TaskDef', taskdef);
                end
                
%                 tbname      = StructBase.getfieldx(taskdef, 'DBTableName');
%                 tbname2     = StructBase.getfieldx(taskdef, 'DBTableName2');
%                 tbname3     = StructBase.getfieldx(taskdef, 'DBTableName3');
                tbname      = GET_TASK_CONFIG(task,'DBTableName');
                if isempty(tbname)
                    tbname  = GET_TASK_CONFIG(task,'DBTableName1');
                end
                
                tbname2     = GET_TASK_CONFIG(task,'DBTableName2');
                tbname3     = GET_TASK_CONFIG(task,'DBTableName3');
                [~, ~, ext] = fileparts(taskfile); 
                
                switch lower(ext)
                    case {'.xlsx', '.csv', '.tskinfo'}
                        ProcessTaskList(self, task, taskfile);
                        processed= true;
                    case {'.tsks'}
                        %[taskname, dbT,  task] = ParseTsksFile(self, taskfile);
                        tasktablefile = GetConfig(task, 'TaskTableFile');
                        if ~isempty(tasktablefile)
                            try
                            res = DosUtil.rdir(tasktablefile); 
                            fnames = {res(:).name};
                            T =table; 
                            for m=1:numel(fnames)
                                fname = fnames{m};
                                T1 = xls.TableBase.ReadTable(fname);
                                T  = cat(1, T, T1); 
                            end
                            catch
                            end
                        else
                            filter= GetConfig(task, 'DBTableFilter');
                            T     = self.m_DB.GetTable_filter(tbname, filter);
                        end
                        if ~isempty(T) 
                            ProcessTaskTable_db(self, task, T);
                        end
                        processed= true;
                    case {'.tsk'}
                        info = [];
                        
                        if ~isempty(tbname) && ~isempty(tbname2)
                            taskuid  = task.getfieldx('TaskUID');
                            res = strsplit(taskuid, '@');
                            taskuid=res{1};
                            uids = strsplit(taskuid, '#');
                            %UID  = uids{1}(4:end); 
                            UID = extractAfter(uids{1}, '.'); 
                            info = self.m_DB.GetTableRow(tbname, UID);
                            if numel(uids)>=2
                                %UID2  = uids{2}(4:end); 
                                UID2 = extractAfter(uids{2}, '.'); 
                                info2 = self.m_DB.GetTableRow(tbname2, UID2);
                                info = {info, info2};
                            end
                            if numel(uids)>=3 && ~isempty(tbname3)
                                %UID  = uids{3}(4:end); 
                                UID3 = extractAfter(uids{3}, '.'); 
                                info3 = self.m_DB.GetTableRow(tbname3, UID3);
                                info = cat(2, info, info3);
                            end
                            if ~isempty(info)
                                processed= ProcessMyTask(self, task, info);
                            end
                        elseif ~isempty(tbname)
                            taskuid  = task.getfieldx('TaskUID');
                            %UID = taskuid(4:end); 
                            UID = extractAfter(taskuid, '.'); 
                            tb  = extractBefore(taskuid, '.'); 
                            info = self.m_DB.GetTableRow(tbname, UID);
                            if isempty(info)
                                %info = self.m_DB.GetTableRow(taskuid(1:2), UID);
                                try %ignore invalid task
                                info = self.m_DB.GetTableRow(tb, UID);
                                catch
                                end
                            end
                            
                            if ~isempty(info)
                                processed= ProcessMyTask(self, task, info);
                            end
                        else
                            info = GET_TASK_CONFIG(task,'TaskInfo', []);
                            processed = ProcessMyTask(self, task, info);
                        end
%                         else
%                             processed= ProcessAdminTask(self, task, info);
                 end
                
                
            catch err
                %processed= true; 
                processed=2;
                self.LogInfo(taskfile); 
                self.LogErr(err);
            end
        end
      
        function processed = ProcessMyTask(self, task, info)
            
            task.RecursiveReplaceTemplates(info);

            %check schedule
            Schedule=GET_TASK_CONFIG(task,'Schedule');
            if ~isempty(Schedule) 
                lastdatetime = GET_TASK_CONFIG(task,'LastExecutionDataTime');
                if  ~self.InScheduleTime(Schedule, lastdatetime)
                    processed = 0; 
                    return;
                end
            end
            
            isAdminTask = GET_TASK_CONFIG(task,'IsAdminTask', 0);
            if isAdminTask
                processed= ProcessAdminTask(self, task, info);
            else
                processed= ProcessTask(self, task, info);
            end
        end

        function LogTask(self, task,  fname)
            recurringperiod =  GET_TASK_CONFIG(task,'Schedule.RecurringPeriod');
            %recurringperiod =  task.getfieldx('Schedule.RecurringPeriod');
            if ~isempty(recurringperiod) && recurringperiod>0
                task.SetConfig('LastExecutionDataTime', datestr(now,'yyyymmddTHHMMSS'));
                task.writeJson(fname);
                return;
            end
            
            taskname = task.getfieldx('TaskName'); 
            subtaskname = task.getfieldx('SubTaskName'); 
            if ~isempty(subtaskname)
                taskname     = [taskname '.' subtaskname];
            end
            try
            if exist(fname, 'file')
                logfolder  = DosUtil.mksubdir(HistoryFolder(self), taskname); 
                [PATHSTR,NAME,EXT] = fileparts(fname);
                dstfile = [logfolder,NAME  EXT];
                movefile(fname, dstfile, 'f'); 
            end
            catch
                self.LogInfo(['error: log task file: ', fname ' move to ' dstfile]);
            end
            %self.LogInfo([taskname ' task file: ', fname ' moved to ' dstfile]);
        end

        function folder = TaskOutputFolder(self, task, info)
            folder = GET_TASK_CONFIG(task,'TaskOutputFolder');
            if ~isempty(folder)
                folder = DosUtil.toFullFile(folder, info, OutputRootFolder(self));
                if ~exist(folder, 'dir')
                    DosUtil.mksubdir(folder);
                end
                return;
            end
            
            if isempty(info)
                folder = OutputRootFolder(self);
                return; 
            end
            % if iscell(info)
            %     info = info{1}; 
            % end
            % 
            % taskuid  = task.getfieldx('TaskUID'); 
            % taskuid1 = strrep(taskuid, '#', '/'); 
            % %folder   = DosUtil.mksubdir(OutputRootFolder(self),[info.PatientID '/' taskuid1]);
            % %customsubfolder = task.getfieldx('CustomSubFolder', ''); 
            % customsubfolder = GET_TASK_CONFIG(task,'CustomSubFolder'); 
            % if ~isempty(customsubfolder)
            %     folder1 = DosUtil.SimplifyPath([OutputRootFolder(self) info.PatientID '/' taskuid1 '/' customsubfolder '/']);
            %     folder  = DosUtil.mksubdir(folder1);
            % else
            %     folder  = DosUtil.mksubdir(OutputRootFolder(self),[info.PatientID '/' taskuid1]);
            % end
            % 
            % folder = DosUtil.SimplifyPath(folder); 

            taskname     = task.getfieldx('TaskName'); 
	        subtaskname  = task.getfieldx('SubTaskName');
	        fulltaskname = taskname;
	        if ~isempty(subtaskname)
		        fulltaskname = [taskname '.' subtaskname];
	        end
	        
	        customsubfolder = GET_TASK_CONFIG(task,'CustomSubFolder', ''); 
	        %if isempty(customsubfolder)
            if strcmpi(customsubfolder, '$FULL_TASK_NAME$')
		        customsubfolder=fulltaskname;
            end

	        taskuid = task.getfieldx('TaskUID'); 
	        taskuid = strrep(taskuid, '#', '/'); 
	        if iscell(info)
		        patid = info{1}.PatientID;
	        else
		        patid = info.PatientID;
	        end
	        
	        folder = [OutputRootFolder(self) patid '/' taskuid '/' customsubfolder '/'];
	        
	        folder = utils.json.TasksDef.WildCardRepStr(folder, info);
        
	        folder = DosUtil.SimplifyPath(folder);
        
	        DosUtil.mksubdir(folder);
        end
        
        function res  = StatusLockFileName(self)
            res = self.getoptioni('TaskStatusLockFile');
        end
        
        function fname = TaskStatusLockFile(self, task, info)
            fname =  [TaskOutputFolder(self, task, info) StatusLockFileName(self)]; 
        end
        
        function UpdateStatus(self, statusfolder, statusstr, varargin)
            self.LogInfo(statusstr); 
%             delete([statusfolder 'status.*']); 
            self.UpdateStatusFile(statusfolder, statusstr, varargin{:});
        end
        
        function ROIMask2StructSet(self, outcfg, outfolder, info)
            imgcfgfile = StructBase.getfieldx_default(outcfg, 'ImageCfgFile', [outfolder '../dcmdata.cfg']);
            imgcfgfile = DosUtil.SimplifyPath(imgcfgfile);
            outroimask = StructBase.getfieldx(outcfg, 'RefROIMaskImage');
            if ischar(outroimask)
                fname =  DosUtil.CatenatePath(outroimask,outfolder);
                outroimask = ROIMaskImage(fname);
            end
            if isfield(outcfg, 'NomenConvert')
                taskdeffolder=TasksDefFolder(self);
                nomenstr = outcfg.('NomenConvert');
                nomenstr.Nomenclature=self.ParseNomenclature(nomenstr, taskdeffolder);
                outcfg.NomenConvert=nomenstr; 
            end
            dcmtk.DcmConverter.OutputStructSet(outroimask, outcfg, outfolder, imgcfgfile);
        end

        function DcmExport(self, task, info)
             if isa(task, 'utils.json.JsonTask')
              outfolder = TaskOutputFolder(self, task, info);
              rsfile    = GET_TASK_CONFIG(task,'InputDcm'); 
              rsfile    = [outfolder rsfile];
             else
                 rsfile    =StructBase.getfieldx(task, 'InputDcm');
                 rootfolder=StructBase.getfieldx(task, 'RootFolder');
                 if ~isempty( rootfolder)
                     rsfile = DosUtil.CatenatePath(rsfile, rootfolder);
                 end
             end
              rsfile    = utils.json.TasksDef.WildCardRepStr(rsfile, info);  
              DcmExportFile(self, task, rsfile); 
        end

        function MergeExportROIMask(self, task, info)  
            outfolder   = TaskOutputFolder(self, task, info);
            taskdeffolder=TasksDefFolder(self);
            %taskname = upper(task.getfieldx('TaskName')); 
            
            origvhfname = GET_TASK_CONFIG(task,'OrigImageHeaderFile');
            origvh =[];
            if ~isempty(origvhfname)
%                 origvh =VolHeader(); 
%                 origvh.readJsonHeader([outfolder origvhfname]);
                origvh = DcmVolHeader(DosUtil.SimplifyPath([outfolder origvhfname]));
            end
            
            preops = GET_TASK_CONFIG(task,'PreProcessing');
            if ~isempty(preops)
                self.ProcessingROIMask([], preops, outfolder);
            end
            
            outmaskimagetype= GET_TASK_CONFIG(task,'OutMaskImageType', 'roimask');
            %task.writeJson([outfolder taskname '.tsk']);
            MergeOps = GET_TASK_CONFIG(task,'MergeOperation');
            
            if ~isempty(MergeOps)
                outroimask = []; 
                if isstruct(MergeOps)
                    MergeOps = arrayfun(@(x)(x), MergeOps, 'uniformoutput', false); 
                end
                for k=1:numel(MergeOps)
                    op=MergeOps{k};
                    %srcroimaskfile = op.SrcRoiMaskFile;
                    srcroimaskfile =StructBase.getfieldx(op, 'SrcRoiMaskFile');
                    if isempty(srcroimaskfile)
                        continue; 
                    end
                    srcroimaskfile = self.toFullFile(srcroimaskfile, info, outfolder); 
                    try
                        srcroimask = ROIMaskImage.SafeLoad(srcroimaskfile); 
                    catch
                        continue;
                    end
                    if ~isempty(origvh)
                        srcroimask.ReformCS(origvh);
                    end
                    if isempty(outroimask)
                        if strcmpi(outmaskimagetype, 'roimask8')
                            outroimask = ROIMask8Image(VolHeaderImage(srcroimask)); 
                        else
                            outroimask = ROIMaskImage(VolHeaderImage(srcroimask)); 
                        end
                        
                        outroimask.imageType=outmaskimagetype; 
                        outroimask.ZeroData(); 
                    end
                    ROIs = StructBase.getfieldx(op, 'ROIs');
                    if isempty(ROIs)
                        outroimask.AddRoiMaskImage(srcroimask);
                    else
                        maskimg = srcroimask.getMergedROIImage(ROIs);
                        maskimg.ReformCS(outroimask); 
                        outroimask.AddRoiMaskImage(maskimg);
                    end
                end
                outroimask.SetDistinguishableColors();

                outroimask.ROIStatTable_regionprops({'ROICenter', 'ROIVolume', 'BoundingBox'}, [],{'modifymetadata', 1});
            else
                inroimasks = GET_TASK_CONFIG(task,'InputRoiMaskFiles');

                inroimasks = strsplit(inroimasks, '|');

                inroimaskfiles = cellfun(@(x)(DosUtil.SimplifyPath([outfolder x])), inroimasks, 'uniformoutput', false);

                selectedroinames = GET_TASK_CONFIG(task,'SelectedROINames');

                outroimask     = ROIMaskImage.MergeROIMaskImages(inroimaskfiles, ...
                    {'selectedroinames',  selectedroinames}, {'output.VolHeader', origvh}, {'outputmask.imagetype', outmaskimagetype});
            end
            
            postops = GET_TASK_CONFIG(task,'PostProcessing');
            if ~isempty(postops)
                self.ProcessingROIMask(outroimask, postops, outfolder);
                outroimask.ROIStatTable_regionprops({'ROICenter', 'ROIVolume', 'BoundingBox'}, [],{'modifymetadata', 1});
            end
            
            %outimgfolder= DosUtil.SimplifyPath([outfolder  '../']); 
            imgcfgfile  = DosUtil.SimplifyPath([outfolder '../dcmdata.cfg']);
            outcfg = GET_TASK_CONFIG(task,'OutputStructSet');

            if ~isempty(outcfg)
                if isfield(outcfg, 'NomenConvert')
                    nomenstr = outcfg.('NomenConvert');
                    nomenstr.Nomenclature=self.ParseNomenclature(nomenstr, taskdeffolder);
                    outcfg.NomenConvert=nomenstr; 
                end
                dcmtk.DcmConverter.OutputStructSet(outroimask, outcfg, outfolder, imgcfgfile);
            end
            
           self.DcmExportROIMaskTask(task, info); 
        end

        function DcmExportROIMaskTask(obj, task, infos, roimask)  
            dcmexport = GET_TASK_CONFIG(task,'DcmExport');
            if ~isempty(dcmexport)
                %dcmtk.DcmConverter.OutputStructSet
                RSSetName=StructBase.getfieldx_default(dcmexport,   'OutRSSetName', 'AUTOSEG');
%                 if isempty(RSSetName) && ~isempty(outcfg)
%                     RSSetName=StructBase.getfieldx(outcfg,   'ID');
%                 end
                if iscell(infos)
                    info1 = infos{1};
                else
                    info1 = infos; 
                end
                outfolder   = TaskOutputFolder(obj, task, info1);
                if ~exist('roimask', 'var') || isempty(roimask)
                    fname = StructBase.getfieldx_default(dcmexport, 'ROIMaskFile', 'labelmask.nii.gz');
                    fname = obj.toFullFile(fname, infos,outfolder);
                    roimask = ROIMaskImage.SafeLoad(fname);
                end
                %outdicomrsfile=[RSSetName '.dcm']; 
                outdicomrsfile   = StructBase.getfieldx_default(dcmexport, 'OutRSFileName', ['RS.AISeg.dcm']); 
                outdicomrsfile   = utils.json.TasksDef.WildCardRepStr(outdicomrsfile, infos);  
                DcmExportROIMask(obj, dcmexport, roimask, outfolder, outdicomrsfile,  RSSetName);  
            end
        end

        function DcmExportROIMask(self, task, outtargetmaskimg, outfolder, outdicomrsfile,  RSSetName)  
            %origvhfname = GET_TASK_CONFIG(task,'OrigImageHeaderFile');
            if isa(task, 'utils.json.JsonTask')
                origvhfname = GET_TASK_CONFIG(task,'OrigImageHeaderFile');
                ROINameDecoration = GET_TASK_CONFIG(task,'ROINameDecoration');
            else
                origvhfname = StructBase.getfieldx(task, 'OrigImageHeaderFile');
                ROINameDecoration = StructBase.getfieldx(task, 'ROINameDecoration');
            end
            if ~isempty(origvhfname)
                %vh =VolHeader(); vh.readJsonHeader([outfolder origvhfname]);
                dcmvh =DcmVolHeader(DosUtil.SimplifyPath([outfolder origvhfname]));
                if abs(dcmvh.z_dim-outtargetmaskimg.z_dim)>0 ||abs(dcmvh.z_pixdim-outtargetmaskimg.z_pixdim)>1e-5||abs(dcmvh.z_start-outtargetmaskimg.z_start)>1e-5
                    outtargetmaskimg.ReformCS(dcmvh);
                    outtargetmaskimg.RemoveROIFields({'zContours', 'xContours', 'yContours'});
                end
            end
            outputrsfile = [outfolder outdicomrsfile]; 
            maskname     =  RSSetName;
            %outputrsfile = [outfolder outdicomrsfile]; 
            rsinfo = dicom.utils.dicom_rtstruct_template;
            rsinfo.StructureSetLabel = maskname; 
            rsinfo.StructureSetName  = maskname; 
            
            dicom.utils.ROIMaskImage2DicomRS(outtargetmaskimg,  dcmvh, [], {'outputfile.rtstruct', outputrsfile}, {'rs.dicominfo', rsinfo}, {'ROINameDecoration', ROINameDecoration});
            %dcmtaskroot  = [DcmServerTasksFolder(self) '/dcmsend/']; 
            self.DcmExportFile(task, outputrsfile);  
        end
        
        function DcmExportFile(self, task, outputrsfile,varargin) 
            dcmtaskroot  = [DcmServerTasksFolder(self) '/dcmsend/']; 
            [exportports, exportfolders] = self.ParseExportPorts(task, {'DcmPortsTable', DcmPortsTable(self)});
            
            if ~isempty(exportports)
                DICOM{1, 1} = outputrsfile;
                dcmT = table(DICOM); 
                %dcmtaskroot  = [DcmServerTasksFolder(self) '/dcmsend/']; 
                if ischar(exportports)
                    exportports = strsplit(exportports, '|'); 
                end
                %[~, name, ext] = fileparts(outputrsfile); 
                name = datestr(now, 'yyyymmddTHHMMSS');
                for k=1:numel(exportports)
                    exportport = exportports{k};
                    taskfolder = DosUtil.mksubdir(dcmtaskroot, exportport); 
                    %[~, outdicomrsfile, ext] = fileparts(outputrsfile);
                    taskfname = [taskfolder name '.xlsx']; 
                    xls.TableBase.WriteTable(dcmT, taskfname); 
                end
            end

            if ~isempty(exportfolders)
                if ischar(exportfolders)
                    exportfolders = strsplit(exportfolders, '|'); 
                end
                [~, name, ext] = fileparts(outputrsfile); 
                for k=1:numel(exportfolders)
                    %exportfolder = DosUtil.mksubdir(exportfolders{k});
                    exportfolder = exportfolders{k};
                    if ~DosUtil.isabsolutepath(exportfolder)
                        exportfolder = [self.RootFolder() exportfolder];
                    end
                    if ~exist(exportfolder, 'dir')
                        DosUtil.mksubdir(exportfolder);
                    end
                    copyfile(outputrsfile, [exportfolder name ext]); 
                end
            end
        end 

        function fname =  CreateTaskFile(self, taskfilename, info)
            fname = '';
            if ~isempty(taskfilename)
                fname = self.toFullFile(taskfilename, info,TaskFolder(self));
                if ~exist(fname, 'file')
                    fclose(fopen(fname, 'w'));
                end
            end
        end
        
        function CalcDcmExportFile(obj,  task, infos)
            evals = GET_TASK_CONFIG(task,'DcmExportFile');
            if isempty(evals)
                %return;
                ports = GET_TASK_CONFIG(task,'DcmExportPort');
                rsfile = GET_TASK_CONFIG(task,'OutputRSFileName');
                if isempty(ports) && isempty(rsfile)
                    return;
                end
                evals = struct('DcmFileName', rsfile, 'DcmExportPort', ports);
            end
            if isstruct(evals)
                evals = arrayfun(@(x)(x), evals, 'uniformoutput', false); 
            end
            outfolder= TaskOutputFolder(obj, task, infos);
            for k=1:numel(evals)
                export  = evals{k};
                dcmfile = StructBase.getfieldx(export, 'DcmFileName');
                if isempty(dcmfile)
                    continue;
                end
                
                dcmfile = obj.toFullFile(dcmfile, infos,outfolder);
                DcmExportFile(obj, export, dcmfile); 
            end
        end
        
        function CalcPostTaskEval(obj,  task, infos)
            evals = GET_TASK_CONFIG(task,'ROIEval');
            if isempty(evals)
                return;
            end
            if isstruct(evals)
                evals = arrayfun(@(x)(x), evals, 'uniformoutput', false); 
            end
            for k=1:numel(evals)
                ROIEval(obj, evals{k},  task, infos);
            end
        end
 
        function CalcPostTaskRecord(obj,  task, infos)
            evals = GET_TASK_CONFIG(task,'Record');
            if isempty(evals)
                return;
            end
            if isstruct(evals)
                evals = arrayfun(@(x)(x), evals, 'uniformoutput', false); 
            end
            for k=1:numel(evals)
                TaskRecord(obj, evals{k},  task, infos);
            end
        end

        function TaskRecord(obj, evalstr,  task, infos)
            if iscell(infos)
                info1 = infos{1};
            else
                info1 = infos; 
            end
            outfolder= TaskOutputFolder(obj, task, info1);
            %taskdeffolder = TasksDefFolder(obj);
            recordfile = StructBase.getfieldx(evalstr, 'RecordFile'); 
            recordfile = obj.toFullFile(recordfile, infos,outfolder);
            cfg = dcmtk.DcmDataConfig(recordfile);
            recordname = StructBase.getfieldx(evalstr, 'RecordName'); 
            recordinfo = StructBase.getfieldx(evalstr, 'RecordInfo'); 
            optype = StructBase.getfieldx_default(evalstr, 'RecordType', 'SetConfig'); 
            switch(optype)
                case 'SetConfig'
                    cfg.SetConfig(recordname, recordinfo);
                case 'AddStruct2List'
                    cfg.AddStruct2List(recordinfo, recordname); 
            end
            cfg.writeJson();
        end


        function T = ROIEval(obj, evalstr,  task, infos)
            if ~exist('infos', 'var')
                infos =[];
            end
            if isa(task, 'char')
                 outfolder= task;
            else
               if iscell(infos)
                    info1 = infos{1};
               else
                    info1 = infos; 
               end
               outfolder= TaskOutputFolder(obj, task, info1);
            end

            taskdeffolder = TasksDefFolder(obj);
            evaltype = StructBase.getfieldx(evalstr, 'EvalType'); 
            
            if isempty(evaltype)
                T=[];
                return; 
            end
            fname    = StructBase.getfieldx(evalstr, 'ROIMaskFile'); 
            roimaskfile = obj.toFullFile(fname, infos,outfolder);
            propnames = StructBase.getfieldx(evalstr,'EvalPropNames');
            roinames  = StructBase.getfieldx(evalstr,'EvalROINames');
            roieval   = ai.seg.ROIEval({'RefROIMaskImage', roimaskfile});  
            if ~isempty(roinames)
                roieval.setOption('ROINames', roinames); 
            end
            
            cfgnames = {'RefPlaceHoldROIs', 'TestPlaceHoldROIs'};
            for k=1:numel(cfgnames)
                cfgname = cfgnames{k};
                if isfield(evalstr, cfgname)
                    val = StructBase.getfieldx(evalstr,cfgname);
                    roieval.setOption(cfgname, val); 
                end
            end

            otheropts = StructBase.getfieldx(evalstr, 'OtherOptions');
            if ~isempty(otheropts)
                otheropts = OptionsMap.struct2options(otheropts);
                roieval.setOptions(otheropts);
            end
            
            str ='RefNomenclature'; nomen = StructBase.getfieldx_default(evalstr, str, []);
            if ischar(nomen) 
                if ~strcmpi(nomen, 'default')
                    nomen = obj.toFullFile(nomen, infos,taskdeffolder);
                end
                roieval.setOption(str, nomen); 
            end
            
            str ='TestNomenclature'; nomen = StructBase.getfieldx_default(evalstr, str, []);
            if ischar(nomen) 
                if ~strcmpi(nomen, 'default')
                    nomen = obj.toFullFile(nomen, infos,taskdeffolder);
                end
                roieval.setOption(str, nomen); 
            end
            
            switch evaltype
                case 'ROIStat'
                    T = roieval.ROIStatTable([], propnames);
                    radiomics = StructBase.getfieldx_default(evalstr, 'Radiomics', 0);
                    
                    if radiomics
                        evalstr1 = evalstr;
                        fname    = StructBase.getfieldx(evalstr, 'EvalImageFile'); 
                        imgfile = obj.toFullFile(fname, infos,outfolder);
                        evalstr1.('EvalROIMaskImage')=roieval.RefROIMaskImage;
                        evalstr1.('EvalImage')=imgfile;
                        evalstr1.('EvalROINames')=T.ROIName;
                        T1 = roieval.CalcRadiomics(evalstr1);
                        T = join(T, T1);
                    end

                case 'DosimetricStat'
                    dvhfile = StructBase.getfieldx_default(evalstr, 'DVHFile', []);
                    if ~isempty(dvhfile) && ischar(dvhfile)
                        dvhfile = obj.toFullFile(dvhfile, infos,outfolder);
                        dvhs = DVHCurves; dvhs.readJson(dvhfile);
                        nomen = roieval.RefNomenclature;
                        if ~isempty(nomen)
                            dvhs.ApplyNomen(nomen);
                        end
                    else
                        dosefile = StructBase.getfieldx_default(evalstr, 'DoseFile', []);
                        dosefile = obj.toFullFile(dosefile, infos,outfolder);
                        dvhs = VolHeaderImage(dosefile); 
                    end
                    T = roieval.DosimetricStatTable(dvhs, propnames);
                
                case 'ROISimilarity'
                    fname    = StructBase.getfieldx(evalstr, 'TestROIMaskFile'); 
                    roimaskfile = obj.toFullFile(fname, infos,outfolder);
                    roieval.setOption('TestROIMaskImage', roimaskfile); 
                    T = roieval.ROISimilarityTable(propnames);

                case 'Radiomics'
                    evalstr1 = evalstr;
                    fname    = StructBase.getfieldx(evalstr, 'EvalImageFile'); 
                    imgfile = obj.toFullFile(fname, infos,outfolder);
                    evalstr1.('EvalROIMaskImage')=roieval.RefROIMaskImage;
                    evalstr1.('EvalImage')=imgfile;
                    T = roieval.CalcRadiomics(evalstr1);
            end
            
            outxlsfname = StructBase.getfieldx_default(evalstr, 'OutXlsFileName');
            if ~isempty(outxlsfname)
                outxlsfname = obj.toFullFile(outxlsfname, infos,outfolder);
                outxlssheet = StructBase.getfieldx_default(evalstr, 'OutXlsSheetName', 'ROIEval');
                outxlssheet = utils.json.TasksDef.WildCardRepStr(outxlssheet, infos); 
                xls.TableBase.WriteTable(T, outxlsfname, 'sheet', outxlssheet);
            end
        end
        
        function CalcPostTaskSignal(obj,  task, infos)
            signals = GET_TASK_CONFIG(task,'PostTaskSignal');
            if isempty(signals)
                return;
            end
            if isstruct(signals)
                signals = arrayfun(@(x)(x), signals, 'uniformoutput', false); 
            end
            
            for k=1:numel(signals)
                PostTaskSignal(obj, signals{k},  task, infos);
            end
        end
        
        function PostTaskSignal(obj, signal, task, infos)
            if iscell(infos)
                info1 = infos{1};
            else
                info1 = infos; 
            end
            outfolder     = TaskOutputFolder(obj, task, info1);
            taskdeffolder = TasksDefFolder(obj);
            signaltype    = StructBase.getfieldx(signal, 'SignalType'); 
            %condition     = StructBase.getfieldx(signal, 'Condition'); 
            switch signaltype
                case 'CreateJson'
                    DosUtil.WatchDog.CreateJson(signal, infos);

                case 'CreateTaskTable'
                    DosUtil.WatchDog.CreateTaskTable(signal, infos);

                case 'Segman.BodyRegion'
                    fname    = StructBase.getfieldx(signal, 'ROIMaskFile'); 
                    roimaskfile = obj.toFullFile(fname, infos,outfolder);
                    labelmask = ROIMaskImage.SafeLoad(roimaskfile); 
                    regiondeffile = StructBase.getfieldx(signal, 'RegionDefFile');
                    if ~isempty(regiondeffile) && ischar(regiondeffile)
                        regiondeffile = obj.toFullFile(regiondeffile, infos,taskdeffolder);
                    end
                    regions   = ai.seg.ImageSeg_CT_segman.LabelBodyRegion(labelmask, regiondeffile); 
%                     signalfiles = cellfun(@(x)([outfolder 'anatomy.' x]), regions, 'uniformoutput', false); 
%                     for k=1:numel(signalfiles)
%                         fclose(fopen(signalfiles{k}, 'wt'));
%                     end
                    str = xls.TableBase.catCell2Str(regions, '_');
                    signalfile = [outfolder 'anatomy.' str];
                    fclose(fopen(signalfile, 'wt'));
            end
        end
        
         function ArchiveFolder(self, archives, info)
            %archives   = GET_TASK_CONFIG(task,'Archive'); 
            if isstruct(archives)
                archives = arrayfun(@(x)(x), archives, 'uniformoutput', false);
            end
            
            rootfolder= RootFolder(self);
            for k=1:numel(archives)
                archive   = archives{k};
                srcfolder = StructBase.getfieldx(archive, 'SrcFolder'); 
                if isempty(srcfolder)
                    continue; 
                else
                    srcfolder = self.toFullFile(srcfolder, info,rootfolder);
                end

                dstfolder = StructBase.getfieldx(archive, 'DstFolder');    
                if ~isempty(dstfolder)
                    dstfolder = self.toFullFile(dstfolder, info,rootfolder);
                end
                DosUtil.Archive.ArchiveSubFolders(srcfolder, dstfolder, archive);
            end
         end

         function WatchDog(self, archives, info)
            if isstruct(archives)
                archives = arrayfun(@(x)(x), archives, 'uniformoutput', false);
            end

            for k=1:numel(archives)
                archive   = archives{k};
                % watchtype = StructBase.getfieldx(archive, 'WatchType'); 
                % switch watchtype
                %     case {'File'}
                %         DosUtilWatchDog.WatchFile(archive, info);
                % end
                DosUtil.WatchDog.WatchFile(archive, info);
            end
         end

         function ExportSubDB(self, taskstr, info, statusfolder)
             % subdbfile = 'C:\temp\RTPlanDB20250624_sub.sqlite';
                    % task = struct('DstDBFile', subdbfile, 'DstXlsFile', strrep(subdbfile, '.sqlite', '.xlsx'), ...
                    %     'ExportFilter', filterT);
             names = {'SrcDBFile', 'DstDBFile', 'ExportFilter', 'DstXlsFile'};
             taskstr = DosUtil.CatenatePaths_struct(taskstr, names, statusfolder);
             srcdbfile = StructBase.getfieldx_default(taskstr, 'SrcDBFile', '');
             srcdbopened = 0; 
             if ~isempty(srcdbfile) &&exist(srcdbfile, 'file')
                 copytolocal = StructBase.getfieldx_default(taskstr, 'CopySrcDBToLocal', 0);
                 if ischar(copytolocal)
                     copytolocal=str2num(copytolocal);
                 end
                 if copytolocal
                     tempsrcdbfile = 'c:\temp\tempSrcDB.sqlite';
                     copyfile(srcdbfile, tempsrcdbfile, 'f');
                     srcdbfile = tempsrcdbfile;
                 end
                 srcdb =mydb.sqlite.DcmRTDB(srcdbfile, {'DBConnection', 3});
                 srcdbopened = 1; 
             else
                 srcdb = self.m_DB;
             end
             srcdb.ExportSubDB(taskstr);
             if srcdbopened
                srcdb.CloseDB();
             end
        end
    end
    
    methods (Static) 
        %%todo: validate this code
        function flag = CheckTaskCondition(conditions, varargin)
            flag = true; 
            conditions = StructBase.toCell(conditions);
            for k=1:numel(conditions)
                flag1 = daemon.DcmRTServer.CheckCondition(conditions{k}, varargin{:});
                flag = flag&flag1; 
                if ~flag
                    return
                end
            end
        end

        function valid = CheckCondition(condstr, info, statusfolder)
            valid = true; 
            info = StructBase.toCell(info);
            condstr = DosUtil.WildCardRepStr_recursive(condstr, info);
            conditiontype = StructBase.getfieldx(condstr, 'ConditionType');
            IsNegateCondition=StructBase.getfieldx_default(condstr, 'IsNegateCondition', false);
            if isempty(conditiontype)
                if IsNegateCondition
                    valid = ~valid;
                end
                return;
            end
            switch conditiontype
                case 'InfoFilter'
                    filterstr      = StructBase.getfieldx(condstr, 'Filter');
                    if isempty(filterstr)
                        return
                    end
                    instancefilter = xls.TableFilter; 
                    instancefilter.AddTableFilter(filterstr);
                    infoseq = StructBase.getfieldx_default(condstr, 'InfoSeq', 1);
                    infoseq = intersect(infoseq, 1:numel(info));
                    for k=1:numel(infoseq)
                        m = infoseq(k);
                        valid1 = instancefilter.FilterStruct(info{m});
                        valid = valid&valid1; 
                        if ~valid 
                            break;
                        end
                    end
                case 'CheckFileOrFolder'
                    if exist('statusfolder', 'var')&&~isempty(statusfolder)
                        condstr = DosUtil.CatenatePath_struct(condstr, 'Name', statusfolder);
                    end
                    [valid, res] = DosUtil.CheckFileOrFolder(condstr);
            end
            
            if IsNegateCondition
                valid = ~valid;
            end
        end

        function nomen=ParseNomenclature(nomenstr, taskdeffolder)
            nomen=[];
            if ~isempty(nomenstr)
                nomenfile = StructBase.getfieldx(nomenstr, 'NomenclatureFileName'); 
                %nomen = Nomenclature(self);
                if ~isempty(nomenfile)
                    nomenfile = daemon.DcmRTServer.toFullFile(nomenfile, [],taskdeffolder);
                    sheetname =  StructBase.getfieldx(nomenstr, 'NomenclatureSheetName'); 
                    opts = OptionsMap.struct2options(nomenstr); 
                    nomen  = ai.rtstruct.Nomenclature({'NomenclatureTable.file', nomenfile}, {'NomenclatureTable.sheet', sheetname}, opts);
                end
            end
        end
        
        function fname = toFullFile(fname, info,rootfolder)
              if exist('info', 'var') && ~isempty(info)
                 fname = utils.json.TasksDef.WildCardRepStr(fname, info); 
              end

              if ~DosUtil.isabsolutepath(fname) && exist('rootfolder', 'var')
                  fname = [rootfolder fname];
              end
              fname = DosUtil.SimplifyPath(fname); 
        end

        function UpdateStatusFile(statusfolder, statusstr, msg)
            if ~exist('msg', 'var')
                fclose(fopen([statusfolder, 'status.' statusstr], 'w'));
            else
                fid = fopen([statusfolder,  'status.' statusstr], 'w');
                fprintf(fid, msg); 
                fclose(fid);
            end
        end
        
        function [exportports, exportfolders] = ParseExportPorts(task, varargin)
            options  = OptionsMap(varargin{:}); 
            T = options.getOption('DcmPortsTable'); 
            
%             exportports = GET_TASK_CONFIG(task,'DcmExportPort'); 
%             if isempty(exportports)
%                  exportports = GET_TASK_CONFIG(task,'DcmExport.DcmExportPort'); 
%             end
            % if isa(task, 'utils.json.JsonTask')
                exportports = GET_TASK_CONFIG(task,'DcmExportPort');
            % else
            %     exportports = StructBase.getfieldx(task,'DcmExportPort'); 
            % end
            if ischar(exportports)
                exportports=strsplit(exportports, '|');
            end
            exportfolders={};
            if ~isempty(T) && ~isempty(exportports)
                portnames   = T.DcmPortName;
                folders     = T.ImportFolderName; 
                [flag, loc] = ismember(exportports, portnames);
                exportports(~flag)=[]; loc(~flag)=[];
                folders=folders(loc);
                I = cellfun(@(x)(~isempty(x)), folders); 
                exportfolders  = folders(I);
                exportports(I) = [];
            end
            
%             exportfolders1 = GET_TASK_CONFIG(task,'DcmExportFolder');
%             if isempty(exportfolders1)
%                  exportfolders1 = GET_TASK_CONFIG(task,'DcmExport.DcmExportFolder'); 
%             end
            if isa(task, 'utils.json.JsonTask')
                exportfolders1 = GET_TASK_CONFIG(task,'DcmExportFolder');
            else
                exportfolders1 = StructBase.getfieldx(task, 'DcmExportFolder');
            end
            if ischar(exportfolders1)
                exportfolders1  = {exportfolders1};
            end
            if ~isempty(exportfolders1)
                exportfolders = cat(1, exportfolders(:), exportfolders1(:));
            end
        end
        
        function AssociateDoc(AssociateDocStr, info, statusfolder)
            AssociateDocStr=DosUtil.RecursiveRepMatchQuoteStrX(AssociateDocStr, info);
            AssociateFileName=StructBase.getfieldx(AssociateDocStr, 'AssociateFileName');
            DocFileName=StructBase.getfieldx(AssociateDocStr, 'DocFileName');
            if ~isempty(AssociateFileName)&&~isempty(DocFileName)
                AssociateFileName=DosUtil.toFullFile(AssociateFileName, [],statusfolder);
                DocFileName=DosUtil.toFullFile(DocFileName, [],statusfolder);
                DocInfo = StructBase.getfieldx(AssociateDocStr,'DocInfo');
                cfg     = utils.json.JsonConfig(AssociateFileName);
                cfg.AddDocument2DocumentSet(DocFileName,DocInfo);
                cfg.writeJson();
            end
        end

        function AssociateStruct2List(AssociateDocStr, info, statusfolder)
            AssociateDocStr=DosUtil.RecursiveRepMatchQuoteStrX(AssociateDocStr, info);
            AssociateFileName=StructBase.getfieldx(AssociateDocStr, 'AssociateFileName');
            ListName = StructBase.getfieldx(AssociateDocStr,'ListName');
            DocInfo = StructBase.getfieldx(AssociateDocStr,'Info');
            %DocFileName=StructBase.getfieldx(AssociateDocStr, 'DocFileName');
            if ~isempty(AssociateFileName)&&~isempty(DocInfo)
                AssociateFileName=DosUtil.toFullFile(AssociateFileName, [],statusfolder);
                %DocFileName=DosUtil.toFullFile(DocFileName, [],statusfolder);
                %DocInfo = StructBase.getfieldx(AssociateDocStr,'DocInfo');
                cfg     = utils.json.JsonConfig(AssociateFileName);
                %cfg.AddDocument2DocumentSet(DocFileName,DocInfo);
                cfg.AddStruct2List(DocInfo, ListName);
                cfg.writeJson();
            end
        end
        
        function response = ExecuteCommand(ExecuteCommandStr, info, statusfolder)
            ExecuteCommandStr=DosUtil.RecursiveRepMatchQuoteStrX(ExecuteCommandStr, info);
            
            Command=StructBase.getfieldx(ExecuteCommandStr, 'CommandFile');
            if ~isempty(Command)
                Command = DosUtil.toFullFile(Command, info, statusfolder); 
            else
                Command=StructBase.getfieldx(ExecuteCommandStr, 'Command');
            end
            
            CommandType=StructBase.getfieldx(ExecuteCommandStr, 'CommandType');
            WorkingDir =StructBase.getfieldx(ExecuteCommandStr, 'WorkingDir');
            if ~isempty(WorkingDir)
                WorkingDir = DosUtil.toFullFile(WorkingDir, info, statusfolder); 
            end
                
            response = DosUtil.ExecuteCommand(Command, CommandType, WorkingDir);
        end
        
        function CopyFile(task, info, srcfolder0)
            if ~exist('srcfolder0', 'var')
                srcfolder0='';
            end
            
            srcfolder      = StructBase.getfieldx_default(task,'SrcFolder', '');
            dstfolder      = StructBase.getfieldx_default(task,'DstFolder', '');
            srcfilenames   = StructBase.getfieldx_default(task,'SrcFileName', '');
            dstfilenames   = StructBase.getfieldx_default(task,'DstFileName', '');
            copymode       = StructBase.getfieldx_default(task,'CopyMode', 'copy');
            if ~isempty(info)
                srcfolder = utils.json.TasksDef.WildCardRepStr(srcfolder, info);
                dstfolder = utils.json.TasksDef.WildCardRepStr(dstfolder, info);
            end
            % if isempty(srcfilenames)
            %     try
            %     switch lower(copymode)
            %         case {'copy'}
            %             copyfile(srcfolder, dstfolder);
            %         case {'move'}
            %             movefile(srcfolder, dstfolder);
            %     end
            %     catch
            %     end
            %     return
            % end

            if ~isempty(srcfilenames)
                srcfilenames   = strsplit(srcfilenames, '|'); 
            end
            if ~isempty(dstfilenames)
                dstfilenames   = strsplit(dstfilenames, '|'); 
            end

            % srcfolder = DosUtil.SimplifyPath([srcfolder0 srcfolder]);
            % dstfolder = DosUtil.SimplifyPath([srcfolder0 dstfolder]);
            srcfolder = DosUtil.CatenatePath(srcfolder, srcfolder0);
            dstfolder = DosUtil.CatenatePath(dstfolder, srcfolder0);
            if isempty(srcfilenames)
                 switch lower(copymode)
                    case {'copy'}
                        copyfile(srcfolder, dstfolder);
                    case {'move'}
                        movefile(srcfolder, dstfolder);
                    case {'link', 'symlink'}
                        linkmode = StructBase.getfieldx_default(task,'LinkMode', '');
                        command = sprintf(['mklink ' linkmode ' "%s" "%s"'], dstfolder, srcfolder);
                        system(command);
                end
            else
            for k=1:numel(srcfilenames)
                try
                srcfile = [srcfolder srcfilenames{k}]; 
                if isempty(dstfilenames)
                    dstfile = dstfolder; 
                    %copyfile(srcfile, dstfolder);
                else
                    dstfile = [dstfolder dstfilenames{k}]; 
                    %copyfile(srcfile, dstfile);
                end
                srcfile = DosUtil.SimplifyPath(srcfile);
                dstfile = DosUtil.SimplifyPath(dstfile);
                switch lower(copymode)
                    case {'copy'}
                        copyfile(srcfile, dstfile);
                    case {'move'}
                        movefile(srcfile, dstfile);
                    case {'link', 'symlink'}
                        command = sprintf('mklink "%s" "%s"', dstfile, srcfile);
                        system(command);
                end
                catch
                end
            end
            end
        end
        
        function flag = InScheduleTime(Schedule, lastdatetime)
            if ~exist('lastdatetime','var')
                lastdatetime=[];
            end
            
            flag = 1;

            hoursofday = StructBase.getfieldx(Schedule, 'HourOfDay'); %in hours
            if ~isempty(hoursofday)
                curhour = hour(now);
                if ~ismember(curhour, hoursofday)
                    flag = 0; 
                    return;
                end
            end
            
            dayofweek = StructBase.getfieldx(Schedule, 'DayOfWeek'); %1 for Sunday, 2 for Monday, ...
            if ~isempty(dayofweek)
                curday = weekday(now);
                if ~ismember(curday, dayofweek)
                    flag = 0; 
                    return;
                end
            end

            dayofmonth = StructBase.getfieldx(Schedule, 'DayOfMonth'); %
            if ~isempty(dayofmonth)
                curday = day(now);
                if ~ismember(curday, dayofmonth)
                    flag = 0; 
                    return;
                end
            end
            
            if ~isempty(lastdatetime)
                if ischar(lastdatetime)
                    lastdatetime = datenum(lastdatetime, 'yyyymmddTHHMMSS');
                end
                Period = StructBase.getfieldx(Schedule,'RecurringPeriod');
                if ~isempty(Period)
                    PeriodUnit = StructBase.getfieldx_default(Schedule,'RecurringPeriodUnit', 'day');
                    switch lower(PeriodUnit)
                        case 'month'
                            period2day = 30; 
                        case 'week'
                            period2day = 7; 
                        case 'day'
                            period2day = 1; 
                        case 'hour'
                            period2day = 1/24; 
                        case 'minute'
                            period2day = 1/(24*60);
                        case 'second'
                            period2day = 1/(24*3600);     
                    end
                    if now-lastdatetime<Period*period2day
                        flag = 0; 
                        return;
                    end
                end
            end
        end
        
        function RefCenter = ParseRefCenter(RefCenter, info, datarootfolder)
            %RefCenter=StructBase.getfieldx(atlasstr, "RefCenter"); 
            if isstruct(RefCenter)
               LabelMaskFile = StructBase.getfieldx(RefCenter, "LabelMaskFile");
               LabelMaskFile = daemon.DcmRTServer.toFullFile(LabelMaskFile, info,datarootfolder);
               
               RefROIName    = StructBase.getfieldx(RefCenter, "RefROIName");
               RefROIProp    = StructBase.getfieldx_default(RefCenter, "RefROIProp", 'ROICenter');
              
               [~, ~, ext]= fileparts(LabelMaskFile);
               if strcmpi(ext, '.json')
                   T = ROIMaskImage.ROIStatTable_header(LabelMaskFile,  RefROIProp, RefROIName); 
               else
                   labelmask     = ROIMaskImage(LabelMaskFile);
                   T = labelmask.ROIStatTable_regionprops(RefROIProp, RefROIName); 
               end
               roicenter = T.(RefROIProp); 
               offset = StructBase.getfieldx_default(RefCenter, "RefCenterOffset", [0 0 0]);
               RefCenter = roicenter(:)'+offset(:)';
           end
        end

        function ExportImage(roimask, outcfg, outfolder)
            outimgfile = StructBase.getfieldx(outcfg, 'ImageFileName');
            if isempty(outimgfile)
                return
            end
            dstvh = StructBase.getfieldx(outcfg, 'DstVolHeader');
            if ~isempty(dstvh)
                if ischar(dstvh)
                    %dstvh =  DosUtil.SimplifyPath([outfolder dstvh]);
                    dstvh = DosUtil.CatenatePath(dstvh, outfolder);
                end
                dstvh = DcmVolHeader(dstvh);
                if isa(roimask, 'ROIMaskImage')
                    roimask = ROIMaskImage(roimask);
                else
                    roimask = VolHeaderImage(roimask);
                end
                roimask.ReformCS(dstvh);
            end
            %dstfile = DosUtil.SimplifyPath([outfolder outimgfile]);
            dstfile = DosUtil.CatenatePath(outimgfile, outfolder);
            [dstfolder] = fileparts(dstfile);
            if ~exist(dstfolder, 'dir')
                DosUtil.mksubdir(dstfolder);
            end
            roimask.writeNiftiImage(dstfile);
        end

        function options = ParseOptionsStruct(optionsstr, options)
            if nargin==1 || isempty(options)
                options = OptionsMap;
            end
            optionsstr = StructBase.toCell(optionsstr); 
            for k=1:numel(optionsstr)
                name = StructBase.getfieldx(optionsstr{k}, 'OptionName');
                if ~isempty(name)
                    val = StructBase.getfieldx(optionsstr{k},  'OptionValue');
                    options.setOption(name, val);
                end
            end
        end

        function WriteInfo2Json(infostr, rootfolder)
            if ~exist('rootfolder', 'var')
                rootfolder ='';
            end
            infofile = StructBase.getfieldx(infostr, 'FileName');
            infofile = DosUtil.CatenatePath(infofile, rootfolder);
            Info = StructBase.getfieldx(infostr, 'Info');
            utils.json.writeJson(Info, infofile);
        end


        function [segmask, mergemask] = TotalSeg(taskstr, rootfolder, varargin)
            if ~exist('rootfolder', 'var')
                rootfolder ='';
            end
            %modeloptfile= StructBase.getfieldx(taskstr, 'ModelOptionFile');
            InputImages = StructBase.getfieldx(taskstr, 'InputImage');
            if ischar(InputImages)
                InputImages=strsplit(InputImages, '|');
            end
            InputImages = StructBase.toCell(InputImages);
            for k=1:numel(InputImages)
                fname = InputImages{k};
                if ischar(fname)
                    fname = DosUtil.CatenatePath(fname, rootfolder);
                    InputImages{k}=VolHeaderImage(fname);
                end
            end

            preprocess = StructBase.getfieldx(taskstr, 'PreProcess');
            if ~isempty(preprocess)
                daemon.AISegServer.ProcessingImage(InputImages, preprocess, rootfolder);
            end

            [segmask, mergemask] = ai.seg.TotalSeg.totalSegImage(InputImages{1}, taskstr, varargin{:});

            % obj  = ai.seg.ImageSeg_nnUNET(modeloptfile, varargin{:});
            % opts = OptionsMap(modeloptfile); 
            % id = opts.getOption('nnunet.TaskName');
            % script  = NetSegScript(obj, id);
            % roimask = script.SegImage(InputImages);
            % label_image_type=opts.getOption('label_image_type');
            % if strcmpi(label_image_type, 'image')
            %     roimask = VolHeaderImage(roimask);
            %     roimask.imageType='image';
            % end
            
            postprocess = StructBase.getfieldx(taskstr, 'PostProcess');
            if ~isempty(postprocess)
                 applytomask = StructBase.getfieldx(taskstr, 'ApplyToMask', 'mergemask');
                 if strcmpi(applytomask, 'segmask') &&~isempty(segmask)
                    daemon.AISegServer.ProcessingROIMask(segmask, postprocess, rootfolder); 
                 elseif strcmpi(applytomask, 'mergemask') &&~isempty(mergemask)
                    daemon.AISegServer.ProcessingROIMask(mergemask, postprocess, rootfolder);  
                 end
            end

            OutputFile = StructBase.getfieldx(taskstr, 'OutputFile');
            if ~isempty(OutputFile)
                OutputFile = DosUtil.CatenatePath(OutputFile, rootfolder);
                segmask.SafeWriteNiftiImage(OutputFile);
                if ~isempty(mergemask)
                    mergemask.SafeWriteNiftiImage([OutputFile '_merge']);
                end
            end
        end

        function [roimask] = nnUNETInference(taskstr, rootfolder, varargin)
            if ~exist('rootfolder', 'var')
                rootfolder ='';
            end
            modeloptfile= StructBase.getfieldx(taskstr, 'ModelOptionFile');
            InputImages = StructBase.getfieldx(taskstr, 'InputImage');
            if ischar(InputImages)
                InputImages=strsplit(InputImages, '|');
            end
            InputImages = StructBase.toCell(InputImages);
            numChs = numel(InputImages);
            for k=1:numChs
                fname = InputImages{k};
                if ischar(fname)
                    fname = DosUtil.CatenatePath(fname, rootfolder);
                    InputImages{k}=VolHeaderImage(fname);
                end
            end

            if numChs>1
                refch = StructBase.getfieldx_default(taskstr, 'ReformCS2Channel', 0);
                if ischar(refch)
                    refch = str2num(refch);
                end
                if refch>0&&refch<=numChs
                    for k=1:numChs
                        if k~=refch
                            InputImages{k}.ReformCS(InputImages{refch});
                        end
                    end
                end
            end
            
            preprocess = StructBase.getfieldx(taskstr, 'PreProcess');
            if ~isempty(preprocess)
                daemon.AISegServer.ProcessingImage(InputImages, preprocess, rootfolder);
            end

            obj  = ai.seg.ImageSeg_nnUNET(modeloptfile, varargin{:});
            opts = OptionsMap(modeloptfile); 
            id = opts.getOption('nnunet.TaskName');
            script  = NetSegScript(obj, id);
            roimask = script.SegImage(InputImages);
            label_image_type=opts.getOption('label_image_type');
            if strcmpi(label_image_type, 'image')
                roimask = VolHeaderImage(roimask);
                roimask.imageType='image';
            end
            
            postprocess = StructBase.getfieldx(taskstr, 'PostProcess');
            if ~isempty(postprocess)
                if strcmpi(label_image_type, 'image')
                    daemon.AISegServer.ProcessingImage(roimask, postprocess, rootfolder);
                else
                    daemon.AISegServer.ProcessingROIMask(roimask, postprocess, rootfolder); 
                end
            end

            OutputFile = StructBase.getfieldx(taskstr, 'OutputFile');
            if ~isempty(OutputFile)
                OutputFile = DosUtil.CatenatePath(OutputFile, rootfolder);
                roimask.SafeWriteNiftiImage(OutputFile);
            end
        end

        function T = ListSubFolders(taskstr, rootpath)
            %objfocal = focal.FocalData();      
            filterinfo = StructBase.getfieldx(taskstr, 'Filter');
            %[PatientID,folderInfo] = objfocal.ListPatient(filterinfo);
            parentPath   = StructBase.getfieldx(taskstr, 'ParentFolder');
            folderInfo = DosUtil.rdir_folder(parentPath, filterinfo);
            folderInfo = folderInfo(:);
            
            datenums  = [folderInfo(:).datenum];
            [datenums, I] = sort(datenums, 'descend');
            folderInfo= folderInfo(I);
            SubFolder = arrayfun(@(x)(x.SubFolder), folderInfo, 'UniformOutput',false);
            LastUpdateTimeStamp = arrayfun(@(x)(datestr(x, 'yyyymmddTHHMMSS')), datenums(:), 'UniformOutput',false);
            TaskUID  = SubFolder;
            T = table(SubFolder, TaskUID, LastUpdateTimeStamp); 
            outxlsfile = StructBase.getfieldx(taskstr, 'OutputFile');
            if ~isempty(outxlsfile) && ischar(outxlsfile)
                outxlsfile = DosUtil.CatenatePath(outxlsfile, rootpath);
                SafeWriteTable(T, outxlsfile);
            end
        end

        function ImportDcmFolder(taskstr, rootpath)
            patid      = StructBase.getfieldx(taskstr, 'SubFolder');
            srcdcmroot = StructBase.getfieldx_default(taskstr, 'SrcDcmRoot', '');
            srcdcmfolder= [srcdcmroot, patid '/'];  
            dcmfilepattern = StructBase.getfieldx_default(taskstr, 'DcmFilePattern', '*.dcm');
            DICOM = {[srcdcmfolder dcmfilepattern]};
            T = table(DICOM);
            importtaskfolder = StructBase.getfieldx_default(taskstr,...
                'ImportTaskFolder', '../Tasks/DataBase/import/');
            if ~isempty(importtaskfolder) && ischar(importtaskfolder)
                importtaskfolder = DosUtil.CatenatePath(importtaskfolder, rootpath);
                SafeWriteTable(T, [importtaskfolder 'patient.' patid '.xlsx']);
            end
        end

        %todo: test this function
        function T = ImportDcmPatient(taskstr, info)
            if ~exist('info', 'var')
                info =[];
            end
            T=[];
            patid      = StructBase.getfieldx(taskstr, 'PatientID');
            srcdcmroot = StructBase.getfieldx_default(taskstr, 'SrcDcmRoot', '');
            srcdcmfolder= [srcdcmroot, patid '/'];    
            srcxlsfile=  StructBase.getfieldx_default(taskstr, 'SrcDcmTableFile',...,
                [srcdcmfolder 'patient.' patid '.xlsx']);
            srcxlsfile=  DosUtil.SimplifyPath(srcxlsfile);
            if ~exist(srcxlsfile, 'file')
                return
            end

            dstdcmroot = StructBase.getfieldx_default(taskstr, 'DstDcmRoot', '../DataBase/DicomData/');
            dstdcmfolder= DosUtil.mksubdir(dstdcmroot, patid); 
            dstxlsfile = StructBase.getfieldx_default(taskstr, 'DstDcmTableFile',...,
                [dstdcmfolder 'patient.' patid '.xlsx']);
            dstxlsfile=  DosUtil.SimplifyPath(dstxlsfile);

            modalities=StructBase.getfieldx(taskstr,'Modality');
            if isempty(modalities)
                [modalities] = sheetnames(srcxlsfile);
            end
            DcmFolder=[];
            srctablefilter = []; 
            srcfilterstr = StructBase.getfieldx(taskstr, 'SrcDcmFilter');
            if ~isempty(srcfilterstr)
                srctablefilter=xls.TableFilter.Factory(srcfilterstr);
            end
            for m=1:numel(modalities)
                modality = modalities{m};

                T0 = xls.TableBase.ReadTable(srcxlsfile, {'table.sheetname', modality});
                if ~isempty(srctablefilter)
                    T0 = srctablefilter.FilterTable(T0);
                end

                if isempty(T0)
                    continue; 
                end
                T1 = xls.TableBase.ReadTable(dstxlsfile, {'table.sheetname', modality});
                SeriesInstanceUID0={};SeriesInstanceUID1={};
                try
                SeriesInstanceUID0 = T0.SeriesInstanceUID; 
                catch
                end
                try
                SeriesInstanceUID1 = T1.SeriesInstanceUID; 
                catch        
                end
                if isempty(SeriesInstanceUID0)
                    continue;
                end

                I = ~ismember(SeriesInstanceUID0, SeriesInstanceUID1);
                
                T0 = T0(I, :);
                if isempty(T0)
                    continue; 
                end
                if ismember('DicomFolder', T0.Properties.VariableNames)
                    DcmFolder1=T0.DicomFolder(I);
                else
                    DcmFolder1=cellfun(@(studyid, seriesid)([srcdcmfolder '/' studyid '/' modality '.' seriesid '/']), ...
                        T0.StudyInstanceUID, T0.SeriesInstanceUID, 'UniformOutput', false); 
                end
                DcmFolder =cat(1, DcmFolder, DcmFolder1);
            end
            if isempty(DcmFolder)
                return
            end
            DICOM = cellfun(@(x)(DosUtil.SimplifyPath([x '*.dcm'])), DcmFolder, 'UniformOutput',false);
            T = table(DICOM);
            importtaskfolder = StructBase.getfieldx_default(taskstr,...
                'ImportTaskFolder', '../Tasks/DataBase/import/');
            if ~isempty(importtaskfolder)
                SafeWriteTable(T, [importtaskfolder 'patient.' patid '.xlsx']);
            end
        end

        function success = RegMapImage(task, infos)
            C = ai.reg.RegTable.ParseRegMatrix(task, infos);

            if isempty(C)
                success = 0; 
                return;
            end
        
            outfolder = StructBase.getfieldx_default(task, 'RootFolder', '');
            fixedimgfilename = GET_TASK_CONFIG(task,'FixedImageFileName');
            %fixedimgfilename = utils.json.TasksDef.WildCardRepStr( fixedimgfilename, infos);
            %fixedimgfilename = DosUtil.SimplifyPath([outfolder  fixedimgfilename]); 
            fixedimgfilename      = DosUtil.CatenatePath(fixedimgfilename, outfolder);
            fixedimg = VolHeaderImage.SafeLoad(fixedimgfilename);
            
            roimaskfilename = GET_TASK_CONFIG(task,'MovingImageFileName'); 
            roimaskfile      = DosUtil.CatenatePath(roimaskfilename, outfolder);
            movingimagetype = GET_TASK_CONFIG(task,'MovingImageType', 'ROIMaskImage');
            if strcmpi(movingimagetype, 'ROIMaskImage')
                roimask = ROIMaskImage.SafeLoad(roimaskfile);
                roimask.RemoveROIFields({'xContours','yContours', 'zContours', 'BoundingBox'});
            else
                roimask = VolHeaderImage.SafeLoad(roimaskfile);
            end

            roimask.ApplyAffineCoorTransform(inv(C));
            roimask.ReformCS(fixedimg); 
            %roimask.Convert2ImageContours;
            
            fixedimgfolder = fileparts(fixedimgfilename); 
            imgcfgfile  = [fixedimgfolder '/dcmdata.cfg'];

            outcfg = GET_TASK_CONFIG(task,'OutputStructSet');
            if ~isempty(outcfg)
                if isfield(outcfg, 'ID')
                    outcfg.ID = utils.json.TasksDef.WildCardRepStr( outcfg.ID, infos);
                end
                dcmtk.DcmConverter.OutputStructSet(roimask, outcfg, outfolder, imgcfgfile);
                success = 1;
                DcmExportROIMaskTask(obj, task, infos);  
            end

            outcfg = GET_TASK_CONFIG(task,'ExportImage');
            daemon.DcmRTServer.ExportImage(roimask, outcfg, outfolder);
        end

        function roimask = ProcessROIMask(taskstr, infos, statusfolder)
            ops = StructBase.getfieldx(taskstr, 'Operation');   
            roimask = StructBase.getfieldx(taskstr, 'SrcROIMaskImage');
            if ~isempty(roimask)&&ischar(roimask)
                roimask = DosUtil.CatenatePath(roimask, statusfolder);
                roimask = ROIMaskImage(roimask);
            end
            daemon.AISegServer.ProcessingROIMask(roimask, ops, statusfolder);
            dstroimaskfile = StructBase.getfieldx(taskstr, 'DstROIMaskFileName');
            if ~isempty(dstroimaskfile)&&ischar(dstroimaskfile)
                dstroimaskfile = DosUtil.CatenatePath(dstroimaskfile, statusfolder);
                roimask.writeNiftiImage(dstroimaskfile);
            end
        end      

        function img = ProcessImage(taskstr, infos, statusfolder)
            ops = StructBase.getfieldx(taskstr, 'Operation');   
            img = StructBase.getfieldx(taskstr, 'SrcImage');
            if ~isempty(img)&&ischar(img)
                img = DosUtil.CatenatePath(img, statusfolder);
                srcimagetype = StructBase.getfieldx_default(taskstr, 'SrcImageType', 'VolHeaderImage');
                if strcmpi(srcimagetype, 'ROIMaskImage')
                    img = ROIMaskImage(img);
                else
                    img = VolHeaderImage(img);
                end
            end

            daemon.AISegServer.ProcessingImage(img, ops, statusfolder);
            dstroimaskfile = StructBase.getfieldx(taskstr, 'DstImageFileName');
            if ~isempty(dstroimaskfile)&&ischar(dstroimaskfile)
                dstroimaskfile = DosUtil.CatenatePath(dstroimaskfile, statusfolder);
                img.writeNiftiImage(dstroimaskfile);
            end
        end

        function img =Reform2AtlasCS(img, op, outfolder)
             if isempty(img)
                imagefname = StructBase.getfieldx(op, 'SrcImageFileName');
                if ~isempty(imagefname)
                    imagefname = DosUtil.toFullFile(imagefname,[], outfolder);
                    imagetype = StructBase.getfieldx_default(op, 'SrcImageType', 'VolHeaderImage');
                    if strcmpi(imagetype, 'ROIMaskImage')
                        img = ROIMaskImage(imagefname);
                    else
                        img = VolHeaderImage(imagefname);
                    end
                end
            end

            atlasstr = StructBase.getfieldx(op, 'AtlasCS');
            if isstruct(atlasstr)
                atlasstr.DataRootFolder=outfolder;
            end
            
            ai.atlas.AtlasCS.ReformImage2AtlasCS(img, atlasstr);
            dstimgfname = StructBase.getfieldx(op, 'DstImageFileName');
            if ~isempty(dstimgfname) && ischar(dstimgfname)
                dstimgfname = DosUtil.toFullFile(dstimgfname,[], outfolder);
                img.writeNiftiImage(dstimgfname);
            end
        end

        
    end
end

function value = GET_TASK_CONFIG(task, name, varargin)
    if isa(task, 'utils.json.JsonTask')
         value = task.GetTaskConfig(name, varargin{:});
    else
         value = StructBase.getfieldx_default(task, name, varargin{:});
    end
end

function SafeWriteTable(T, fname, varargin)
    [folder]=fileparts(fname);
    if ~exist(folder, 'dir')
        mksubdir(folder);
    end
    xls.TableBase.WriteTable(T, fname, varargin{:});
end