classdef nnAE < daemon.taskdef.nnUNETInference
    properties
        
    end

    methods
        function obj = nnAE(varargin)         
            <EMAIL>(varargin{:});
        end
    end

    methods (Static)
        function Factory(tasksdeffile)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            %taskdefname  = ['RP_FluenceMotion'];
            nnUNETModelRoot = 'C:\ARTDaemon\distribute\nnUNETDatav2.totalseg\'; 
            datesetnums ={'325', '330'};
            modalities = {'vbs', 'organs30'};
            TrainerName ='#nnUNetTrainerNoMirroring__nnUNetPlans__3d_AE#fold_0';
            modelids = cellfun(@(num, modality)(['Dataset' num '_LPS_3x3x5_labelmask_' modality '2' modality TrainerName]), ...
                 datesetnums, modalities, 'UniformOutput',false);
            
            for k=1:numel(modelids)
                modelid = modelids{k};
                ModelOptionFile=DosUtil.SimplifyPath([nnUNETModelRoot modelid '.opt']);
                modality = modalities{k};
                InputImage ='[InLabelMask]';
                OutputFile ='[OutLabelMask]';
               
                taskdefname = ['temp_nnAE_' modality];
                obj = daemon.taskdef.nnAE({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname}, ...
                    {'ModelOptionFile', ModelOptionFile}, ...
                    {'InputImage',  InputImage}, ...
                    {'OutputFile', OutputFile});
                Processes=[]; m=0;
  
                ROIEval = struct( "OperationType", 'ROIEval',...
                    "EvalType", "ROISimilarity",...
                    "ROIMaskFile", InputImage, ...
                    "TestROIMaskFile", [OutputFile '.nii.gz'],...
                    "OutXlsFileName", "../ROIStat.xlsx",...
                    "OutXlsSheetName", ['nnAE_' modality]...
                    );
                m=m+1; Processes{m} = ROIEval; 
                Dependency = struct("filename", InputImage);
                obj.Generate(tasksdeffile, Dependency, Processes);
            end

            for k=1:numel(modelids)
                modality = modalities{k};
                InputImage =['../nnunet_LPS_1.5x1.5x3_CT2' modality '/labelmask.nii.gz'];
                OutputFile ="labelmask";
                %dependtask ="../DcmConverter/DCMCONVERT_PT/PT.[SeriesInstanceUID].tsk";
               
                taskdefname = ['MR_SynCTm_nnAE_' modality];
                obj = daemon.taskdef.RtPlan({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
               
               CustomSubFolder = taskdefname;
               
               Dependency=[];  n=0; 
               n=n+1;  Dependency{n} = struct("filename", InputImage, ...
                    "taskfilename", "../MetsSeg/nnUNet_MR2SynCT_organs30/MR.[SeriesInstanceUID].tsk"); 
               TaskInfo=struct("FileName", ['temp_nnAE_' modality '/MR.[SeriesInstanceUID].tskinfo']);
               TaskInfo.Info=struct(...
					"TaskOutputFolder", ['[PatientID]/MR.[SeriesInstanceUID]/' CustomSubFolder '/'],...
					"InLabelMask", InputImage, ...
					"OutLabelMask", OutputFile);
                n=n+1; Dependency{n}=struct("filename",     "labelmask.nii.gz",...
                    "TaskInfo", TaskInfo);
                
                Processes=[]; m=0; 
                OutputStructSet=struct("OperationType", 'OutputStructSet',...
                    "ROIMaskImage", OutputFile, ...
                    "LabelMaskFileName", "labelmask",...
                    "LabelMaskWithImageContours", 1);
                 m=m+1; Processes{m} = OutputStructSet; 
       
                 AssociateStruct=struct("OperationType", 'AssociateStruct',...
                      "AssociateFileName", "../dcmdata.cfg",...
                      "ListName", "StructSet", ...
                      "Info", struct(...
                        "ID", ['SynCT_nnAE_' modality],...
                        "LabelMaskFileName", [CustomSubFolder '/labelmask'])...
                        );
                 m=m+1; Processes{m} = AssociateStruct; 

                obj.Generate(tasksdeffile, Dependency, Processes, {'CustomSubFolder', CustomSubFolder});
            end
        end
    end
end