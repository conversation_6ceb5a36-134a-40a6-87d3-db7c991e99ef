classdef qMRI <daemon.taskdef.TaskDef
    properties

    end

    methods
        function obj = qMRI(varargin)
            <EMAIL>(varargin{:});
        end
    end

    methods (Static)
          function keyword = MRDescription_keyword()
                % MRDescription_keyword
                % Returns a struct of MRI sequence keyword regex patterns
            
                sep = '[\s_\-]*'; % flexible separator: space, underscore, hyphen
            
                % Base keywords
                keyword.T1       = ['T1' sep '(W|WI|WEIGHTED|SE|FSE)?|MPRAGE|MP' sep '?RAGE|SPGR|FSPGR|FFE|' ...
                                    'VIBE|LAVA|THRIVE|STARVIBE|FLASH|GRE|T1FLASH|T1IR|T1SE'];
                keyword.T2       = ['T2' sep '(W|WI|WEIGHTED)?|STIR|FSE|TSE|CISS|SPACE|VISTA|CUBE|' ...
                                    'PROP(?:ELLER)?|BLADE|FIESTA|TRUEFISP|BSSFP|DRIVE'];
                keyword.FLAIR    = 'FLAIR|TIRM|TIR';
                keyword.T1FLAIR  = 'T1.*FLAIR|FLAIR.*T1';
                keyword.DWI      = 'DWI|DIFFUSION|ADC|TRACE';
                keyword.CONTRAST = 'POST|GAD|CONTRAST|CE|\+C|C\+|T1C|T1CE|MASTIR|MASTAR|ENH|ENHANCED|(STAR)?VIBE|LAVA|THRIVE';
         end
        
        
        function pattern = MRDescription_pattern()
            % MRDescription_pattern
            % Builds regex patterns for MRI sequences using keywords
        
            kw = daemon.taskdef.qMRI.MRDescription_keyword();
        
            % T1 contrast-enhanced (T1c): T1 + contrast, exclude T2
            pattern.T1c = ['(?=.*(' kw.T1 '))(?=.*(' kw.CONTRAST '))(?!.*(' kw.T2 '))'];
        
            % T1 non-contrast (T1nc): T1 but exclude contrast
            pattern.T1nc = ['(?=.*(' kw.T1 '))(?!.*(' kw.CONTRAST '))'];
        
            % T1FLAIR
            pattern.T1FLAIR = ['(?=.*(' kw.T1FLAIR '))'];
        
            % T1 general (either T1 or contrast)
            pattern.T1 = ['(?=.*(' kw.T1 '))|(?=.*(' kw.CONTRAST '))'];
        
            % FLAIR: contains FLAIR, exclude T1FLAIR and T1
            pattern.FLAIR = ['(?=.*(' kw.FLAIR '))(?!.*(' kw.T1FLAIR '))(?!.*(' kw.T1 '))'];
        
            % T2 (non-FLAIR): T2 keywords, exclude FLAIR and T1
            pattern.T2 = ['(?=.*(' kw.T2 '))(?!.*(' kw.FLAIR '))(?!.*(' kw.T1 '))'];
        
            % DWI
            pattern.DWI = ['(?=.*(' kw.DWI '))'];
        
            % Anchor patterns to full string
            fns = fieldnames(pattern);
            for k = 1:numel(fns)
                fn = fns{k};
                pattern.(fn) = ['^' pattern.(fn) '.*$'];
            end
        end

        function [map, pattern] = MRDescriptionMap_regexp()   
            % T2(?!.*FLAIR)              # T2 not followed later by FLAIR
            % |T2W(?!.*FLAIR)            # T2W not followed by FLAIR
            % |T2WI(?!.*FLAIR)           # T2WI not followed by FLAIR
            % |T2[- ]?weighted(?!.*FLAIR)#
            % |STIR                      # STIR (suppressed fat, still T2W)
            % |FSE                       # Fast spin echo
            % |TSE                       # Turbo spin echo
            % |CISS                      # High-res T2-like
            % |SPACE                     # Siemens 3D T2
            % |VISTA                     # Philips 3D T2
            % |CUBE                      # GE 3D T2
            % |PROP(?:ELLER)?            # Radial T2 (PROP, PROPELLER)
            % |BLADE                     # Siemens radial T2
            % |FIESTA                    # GE bSSFP
            % |TrueFISP                  # Siemens bSSFP
            % |bSSFP                     # Balanced SSFP
            % |DRIVE                     # Philips 3D T2
            
            pattern.T2  = [
              '^(?!.*(FLAIR|T1W|T1WI|T1[-_ ]?WEIGHTED|T1))' ...  % ⛔ exclude entire string if T1-related
              '.*?' ...
              '([_\-\s]|^)' ...
              '(T2(W|WI)?' ...
              '|T2[-_ ]?WEIGHTED' ...
              '|STIR' ...
              '|FSE|TSE|CISS|SPACE|VISTA|CUBE' ...
              '|PROP(?:ELLER)?|BLADE|FIESTA|TRUEFISP|BSSFP|DRIVE)' ...
              '([_\-\s]|$)'
            ];


            pattern.FLAIR = [
              '^(?!.*(T1W|T1WI|T1[-_ ]?WEIGHTED|T1))' ...  % Exclude entire string if any form of T1 is present
              '.*?' ...
              '(FLAIR' ...
              '|T2[\s_\-]?FLAIR' ...
              '|FLAIR[\s_\-]?T2' ...
              '|IR[\s_\-]?(T2|FSE|TSE)?[\s_\-]?FLAIR' ...
              '|FLAIRV\d*' ...
              '|FLUID[\s_\-]?ATTENUATED)' ...
              '([\s_\-]|$)'
            ];

            % pattern_T1 = ['(^|[_\-\s])((T1(W|WI)?|T1[-_ ]?weighted)' ...            % native T1
            %     '|T1[-_ ]?\+[-_ ]?C' ...                                           % T1+C
            %     '|T1[-_ ]?(POST|GAD|CONTRAST|CE)' ...                             % T1 POST/GAD/CE
            %     '|POST[-_ ]?(T1|GAD|CON|CONTRAST)' ...                           % POST T1
            %     '|GAD[-_ ]?T1|GADO|T1C|T1CE' ...                                % GAD variants
            %     '|VIBE|STARVIBE|MP[_\-\s]?RAGE|FLASH|GRE|MP2RAGE|LAVA|SPGR|FSPGR|BRAVO|THRIVE|TFE|FFE|mASTAR)' ... % vendor sequences
            %     '($|[_\-\s])'];

            pattern.T1 =[
              '(^|[_\-\s])' ...
              '(?!.*FLAIR)' ...  % Exclude sequences containing 'FLAIR'
              '[a-zA-Z0-9]*?' ...
              '(T1(W|WI)?' ...
              '|T1[-_ ]?weighted' ...
              '|T1W' ...
              '|MP[_\-\s]?RAGE|MPR' ...
              '|SPGR|FSPGR' ...
              '|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO' ...
              '|VIBE|STARVIBE|LAVA|THRIVE|T1C|T1CE|mASTAR)' ...
              '([_\-\s]|$)'
            ];
            % Vendor	Sequence Name	Type	Contrast Use
            % Siemens	VIBE|STARVIBE	3D gradient echo (GRE)	Pre- & Post-contrast
            % Siemens	MPRAGE	3D inversion recovery GRE	Mostly pre-contrast
            % Siemens	FLASH	2D/3D GRE	Pre-contrast
            % Siemens	GRE	Generic gradient echo	Pre-contrast
            % Siemens	MP2RAGE	T1-weighted quantitative mapping	Research/Pre-contrast
            % GE	LAVA	3D GRE (similar to VIBE|STARVIBE)	Post-contrast
            % GE	SPGR / FSPGR	3D T1-weighted GRE	Pre- & Post-contrast
            % GE	BRAVO	3D inversion recovery GRE	Pre-contrast
            % Philips	THRIVE	3D GRE (equivalent to VIBE|STARVIBE/LAVA)	Post-contrast
            % Philips	TFE / 3D-TFE	Turbo Field Echo (GRE variant)	Pre-contrast
            % Philips	FFE	Fast Field Echo	Pre-contrast
            % Canon	mASTAR	3D T1 GRE	Post-contrast
            % Canon	3D T1 FFE	Fast Field Echo	Pre- & Post-contrast
            
          pattern.T1c =   [
              '(^|[\s_\-])' ...
              '.*?' ...
              '(' ...
                '(POST|GAD|CONTRAST|CE|\+C).*?(T1W|T1(W|WI)?|T1[-_ ]?WEIGHTED|MP[\s_\-]?RAGE|MPR|SPGR|FSPGR|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO)' ...
                '|' ...
                '(T1W|T1(W|WI)?|T1[-_ ]?WEIGHTED|MP[\s_\-]?RAGE|MPR|SPGR|FSPGR|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO).*?(POST|GAD|CONTRAST|CE|\+C)' ...
                '|' ...
                'VIBE|STARVIBE|LAVA|THRIVE|T1C|T1CE|MASTAR' ...
              ')' ...
              '([\s_\-]|$)'
            ];
          
          pattern.T1FLAIR = '(^|[\s_\-]).*?T1.*FLAIR([\s_\-]|$)';

          pattern.T1nc = [
              '^(?!.*(POST|GAD|CONTRAST|CE|\+C|VIBE|STARVIBE|LAVA|THRIVE|T1C|T1CE|MASTAR))' ...
              '.*?' ...
              '([_\-\s]|^)' ...
              '[A-Z0-9]*?' ...
              '(T1(W|WI)?|T1[-_ ]?WEIGHTED|T1W|MP[\s_\-]?RAGE|MPR|SPGR|FSPGR|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO)' ...
              '([_\-\s]|$)'
            ];

          pattern.DWI = [
              '(^|[_\-\s])' ...
              '[a-zA-Z0-9]*?' ...
              '(DWI(_?EPI)?' ...
              '|EPI[_\- ]?DWI' ...
              '|Diffusion(_?Weighted)?' ...
              '|DTI(_\d+dir)?' ...
              '|ADC)' ...
              '([_\-\s]|$)'
            ];

          pattern.T1c_or_FLAIR = [
              '(^|[\s_\-])' ...
              '.*?' ...
              '(' ...
                '(' ...  % T1-weighted with contrast
                  '(POST|GAD|CONTRAST|CE|\+C).*?(T1W|T1(W|WI)?|T1[-_ ]?weighted|MP[\s_\-]?RAGE|SPGR|FSPGR|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO)' ...
                  '|' ...
                  '(T1W|T1(W|WI)?|T1[-_ ]?weighted|MP[\s_\-]?RAGE|MPR|SPGR|FSPGR|FLASH|GRE|FFE|TFE|MP2RAGE|BRAVO).*?(POST|GAD|CONTRAST|CE|\+C)' ...
                  '|' ...
                  'VIBE|STARVIBE|LAVA|THRIVE|T1C|T1CE|mASTAR' ...
                ')' ...
                '|' ...
                '(' ...  % FLAIR
                  'FLAIR' ...
                  '|T2[\s_\-]?FLAIR' ...
                  '|FLAIR[\s_\-]?T2' ...
                  '|IR[\s_\-]?(T2|FSE|TSE)?[\s_\-]?FLAIR' ...
                  '|FLAIRv\d*' ...
                  '|Fluid[\s_\-]?Attenuated' ...
                ')' ...
              ')' ...
              '([\s_\-]|$)'
            ];
           fns = fieldnames(pattern);
           map = OptionsMap();
           for k=1:numel(fns)
               fn = fns{k};
               map.setOption(fn, pattern.(fn));
           end

            % map.setOption('T1', 'T1|BRAVO|MPRANGE|FSPGR');
            % map.setOption('T1_noce', '^(?!.*(POST|GAD|+C|Flair)).*(T1|BRAVO|MPRANGE|FSPGR).*');
            % map.setOption('T1ce', '^(?!.*Flair).*((T1|BRAVO|MPRANGE).*(POST|GAD|+C))|((POST|GAD|+C).*(T1|BRAVO|MPRANGE)).*');
            % map.setOption('T2', '^(?!.*Flair).*(T2|CISS).*');
            % map.setOption('Flair', '^(?!.*(T1|BRAVO|MPRANGE|FSPGR)).*Flair.*');
        end

        function [filters, includedseqs] = MRDescriptionFilters(includedseqs, varargin)
            map = daemon.taskdef.qMRI.MRDescriptionMap_regexp();
            if ~exist("includedseqs", 'var')||isempty(includedseqs)
                includedseqs = map.getOptionNames; 
            end
            m=0; filters=[]; 
            for k=1:numel(includedseqs)
                seq = includedseqs{k};
                val = map.getOptioni(seq);
                if ~isempty(val)
                    m=m+1; 
                    filters{m}   = struct("FieldName", 'SeriesDescription', 'MatchMode', 'regexpi', "FieldValue", val,  "Inclusion", true);           
                end
            end
        end

        function TaskDef_filterMRDescriptions(tasksdeffile, taskdefname,includedseqs, varargin)
            map = daemon.taskdef.qMRI.MRDescriptionMap_regexp();
            m=0; filters=[]; taskfiles=[];
            for k=1:numel(includedseqs)
                seq = includedseqs{k};
                val = map.getoptioni(seq);
                if ~isempty(val)
                    m=m+1; 
                    filters{m}   = struct("FieldName", 'SeriesDescription', 'MatchMode', 'regexpi', "FieldValue", val,  "Inclusion", true);           
                    taskfiles{m} = [taskdefname '_' seq '/[Modality].[SeriesInstanceUID].tsk'];
                end
            end
            daemon.taskdef.TaskDef.TaskDef_filters(tasksdeffile, taskdefname, filters, taskfiles, varargin{:});
       end


        function filters = MRSeqFilter(seq, minslice)
            if ~exist("minslice", 'var')
                minslice  =0; 
            end
            [filters] = daemon.taskdef.qMRI.MRDescriptionFilters(seq);
            if minslice>0
                filter_slice = struct("FieldName", 'NumberOfSlices',	'FieldValue', [minslice, 1000], "MatchMode", 'range');
                filters =cat(2, filters, {filter_slice});
            end
        end

        function WriteMRFilters(dstfolder)
            [filters, includedseqs] = daemon.taskdef.qMRI.MRDescriptionFilters();
            % folder0   = [folder0 'ServiceDcmConverter\tasksdef\'];
            % dstfolder = mksubdir(folder0, 'ImageFilter');
            filter_modality= struct("FieldName", 'Modality', 'MatchMode', "strcmp",	"FieldValue", 'MR',    "Inclusion", true); 
            filter_slice10 = struct("FieldName", 'NumberOfSlices',	'FieldValue', [10, 1000], "MatchMode", 'range');
            Filter  = {filter_slice10};
            dstfile = [dstfolder 'Minslice10.filter'];
            utils.json.writeJson(Filter, dstfile);           
            Filter  = {filter_modality, filter_slice10};
            dstfile = [dstfolder 'MR_Minslice10.filter'];
            utils.json.writeJson(Filter, dstfile);
            for k=1:numel(filters)
                Filter  = {filter_modality, filters{k}, filter_slice10};
                seq     = includedseqs{k};
                dstfile = [dstfolder 'MR_' seq '_Minslice10.filter'];
                utils.json.writeJson(Filter, dstfile);
                Filter  = {filters{k}};
                dstfile = [dstfolder seq '.filter'];
                utils.json.writeJson(Filter, dstfile);
                
                Filter  = {filters{k}, filter_slice10};
                seq     = includedseqs{k};
                dstfile = [dstfolder seq '_Minslice10.filter'];
                utils.json.writeJson(Filter, dstfile);
            end
        end
        
        function ListPatient(tasksdeffile, rootfolder, prefix,  elapsedday)
            %shortname = 'qMRI_ListPatient'; 
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            if ~exist('elapsedday', 'var')
                taskdefname=['temp_' prefix '_ListPatient_DateTimeRange'];
                Filter = struct('DateTimeRange', '[DateTimeRange]');
            else
                [timestr, timeunit]=utils.json.TaskDef.ConvertDaysToTimeStr(elapsedday);
                taskdefname=[prefix '_ListPatient_' timestr timeunit];
                activedate=-1*elapsedday;
                Filter = struct('DateTimeRange', [activedate 1]);
            end

            obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            %rootfolder = '../qMRIRoot/';
            m=0;   Process=[];
           
            OutputFile= ['../DataBase/' prefix '/'  taskdefname '_tsk.xlsx'];
            m=m+1; Process{m}=struct('OperationType', 'ListSubFolders','Filter',Filter, 'ParentFolder', rootfolder, 'OutputFile', OutputFile);                    
            obj.Generate(tasksdeffile, [], Process, {'IsAdminTask', 2});
        end     
       

        function ListPatient_elapseddays(tasksdeffile, elapseddays)
            
            rootfolder = '../Dcm_qMRI/*/';
            prefix = 'qMRI';
            daemon.taskdef.qMRI.ListPatient(tasksdeffile, rootfolder, prefix);
            for k=1:numel(elapseddays)
                elapsedday=elapseddays(k);
                daemon.taskdef.qMRI.ListPatient(tasksdeffile, rootfolder, prefix, elapsedday);
            end
        end


        function obj = temp_ImportDcmFolder(tasksdeffile,taskdefname, SrcDcmRoot)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            optype = 'ImportDcmFolder'; 
            %taskdefname = 'temp_ImportDcmFolder';
            if ~exist('SrcDcmRoot', 'var')
                SrcDcmRoot='[SrcDcmRoot]';
            end

            obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
            m=0; Process=[]; Dependency=[];
            m=m+1; Process{m}   = struct('OperationType',optype,...
                    'SrcDcmRoot', SrcDcmRoot,...
                    'SubFolder',  '[SubFolder]', ...
                    'DcmFilePattern', '[DcmFilePattern]', ...
                    'ImportTaskFolder', '[ImportTaskFolder]');
            DefaultSettings = struct('DcmFilePattern', '*.dcm', 'ImportTaskFolder', '../Tasks/DataBase/import/');
            obj.Generate(tasksdeffile, Dependency, Process, {'IsAdminTask', 2}, {'DefaultSettings', DefaultSettings});
        end

        function qMRI_ImportPatients(tasksdeffile)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            exts = {'_5minute', '_1hour', '_1day', '_1week', '_1month', '_1year', '_10year'};
            for k=1:numel(exts)
                ext = exts{k};
                taskdefname  = ['qMRI_ImportPatients' ext];
                obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
                Dependency=[];
                m=0; Process=[];
                listtaskname = ['qMRI_ListPatient' ext];
                m=m+1; Process{m}   = struct('OperationType','CreateTaskFile',...
                    'TaskFileName', [ listtaskname '/EVERYONE.tsk']);
                srcfolder = '../DataBase/qMRI/';
                dsttsknames = {'temp_qMRI_ImportDcmFolder'};
                for n=1:numel(dsttsknames)
                    dsttskname = dsttsknames{n}; 
                    dsttaskfolder = ['../Tasks/qMRI/' dsttskname '/'];
                    m=m+1; Process{m}   = struct('OperationType','CopyFile',...
                        'SrcFileName',[srcfolder listtaskname '_tsk.xlsx'],...
                        'DstFileName', [dsttaskfolder listtaskname '_tsk.xlsx']);
                end
                obj.Generate(tasksdeffile, Dependency, Process, {'IsAdminTask', 2});
            end
        end

        function MR2SynCT(tasksdeffile, subtaskname, modeloptfiles, seqs, augmentprocess, varargin)
            if ~exist('augmentprocess', 'var')
                augmentprocess=[];
            end
            modality = 'MR'; label_image_type='image';
            % optroot = 'C:\ARTDaemon\distribute\nnUNETDatav2.GK-Hippo\';
            % seqs    = {'T1', 'T2', 'Flair'};
            % datasetnums = {'611', '612', '613'};
            taskprefixs = {'temp_', 'MR_'};
            %for astemp=[0 1]
            for m=1:numel(taskprefixs)
                taskprefix = taskprefixs{m};
                ExtraDef =[];
                if m==2
                    ExtraDef = struct('CustomSubFolder', subtaskname);
                end
                for k=1:numel(seqs)
                    seq = seqs{k};
                    modeloptfile = DosUtil.SimplifyPath(modeloptfiles{k});
                    %taskdefname0=[subtaskname '_' seq];
                    %daemon.taskdef.TaskDef.nnUNETInference(tasksdeffile, modality, subtaskname, taskdefname0, modeloptfile, label_image_type, astemp, augmentprocess);
                    taskdefname=[taskprefix subtaskname '_' seq];
                    daemon.taskdef.nnUNET.InferenceDef(tasksdeffile, taskdefname, modeloptfile, {'augmentprocess', augmentprocess}, {'ExtraDef', ExtraDef }, varargin{:});
                end
            end

            taskdeffolder = [fileparts(tasksdeffile) '/'];
            taskdefname0  = ['MR_' subtaskname '2Dcm'];
            %ctfile ="../[ReferencedImageModality].[ReferencedImageSeriesUID]/image.nii.gz";
            CustomSubFolder=subtaskname;
            synctfile =['image.nii.gz'];
            obj = daemon.taskdef.qMRI({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname0});
            obj.SetDefaultCfg();
            Dependency=struct(...
	     	    "filename",     synctfile,...
	     	    "taskfilename", ['MR_' subtaskname '/MR.[SeriesInstanceUID].tsk']);
            tempfname= ['../image_dcm.json'];
            copytags = cat(2, dicom.utils.VhImage2Dcm.Tags_Patient(), dicom.utils.VhImage2Dcm.Tags_Study());
            copytags = xls.TableBase.Content2Str(copytags);
            dcminfos{1} = struct('FileName', tempfname, 'CopyTagName', copytags);
            dcminfos{2} = struct('SeriesDescription', 'MR2SynCT', 'Manufacturer', 'MR2SynCT');
            Process = struct('OperationType','VolImage2Dcm','VolImage',synctfile, 'Modality', 'CT', 'OutputFolder','dcm/');
            %Process = str; 
            Process.DcmInfo=dcminfos; 
            Process.OutputDcmVhFile='image_dcm.json';
            
            Processes =StructBase.toCell(Process);
            % if exist('augmentprocess', 'var')
            %     Processes = cat(2,  StructBase.toCell(augmentprocess), Processes); 
            % end

            obj.Generate(tasksdeffile, Dependency, Processes, {'CustomSubFolder', CustomSubFolder});

            taskdefname  = ['MR_' subtaskname '2TPS'];
            %ctfile ="../[ReferencedImageModality].[ReferencedImageSeriesUID]/image.nii.gz";
            CustomSubFolder=subtaskname;
            
            obj = daemon.taskdef.qMRI({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            obj.SetDefaultCfg();
            Dependency=struct(...
	     	    "filename",     [taskdefname0 '.tsk'],...
	     	    "taskfilename", [taskdefname0  '/MR.[SeriesInstanceUID].tsk']);
            Process =struct('OperationType','ExportDcm', ... 
                'InputDcm', ['dcm'],...
                'DcmExportPort', 'TPS');
            obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', CustomSubFolder}, {'IsCustomTask', 1});
        end

        function MR2SynCTHead(tasksdeffile)
            subtaskname='SynCTHead'; 
            optroot = 'C:\ARTDaemon\distribute\nnUNETDatav2.GK-Hippo\';
            seqs    = {'T1', 'T2', 'Flair'};
            datasetnums = {'611', '612', '613'};
            
            %AtlasCSFile = '../../../../ServiceqMRI/tasksdef/AtlasCS_MROPHead.json';
            AtlasCSFile = '../../../../ServiceqMRI/tasksdef/AtlasCS_MROPHead_1mm.json';
            AtlasCS = struct('TemplateCS', AtlasCSFile); 
            Reform2AtlasCS = struct('OperationType', 'Reform2AtlasCS', 'AtlasCS', AtlasCS);
            %InferenceCfg = struct("PreProcess", Reform2AtlasCS);
            InferenceCfg=[];

   
            ReformCSProcess = struct('OperationType', 'ProcessImage', 'Operation',Reform2AtlasCS,...
                'SrcImage', 'image.nii.gz', 'DstImageFileName', 'image');

            modeloptfiles = cellfun(@(seq, num)([optroot 'Dataset' num '_GK-Hippo_' seq '2CTm#nnUNetTrainer__nnUNetPlans__3d_fullres#fold_0.opt']), ...
                seqs, datasetnums, 'UniformOutput',false); 
            daemon.taskdef.qMRI.MR2SynCT(tasksdeffile, subtaskname, modeloptfiles, seqs, ReformCSProcess, {'InferenceCfg', InferenceCfg});
            daemon.taskdef.TaskDef.TaskDef_filterMRDescriptions(tasksdeffile, ['MR_' subtaskname], seqs, {'DBTableName', 'MR'});

            subtaskname='SynCTHeadX';
            augmodeloptfile = DosUtil.SimplifyPath('C:\ARTDaemon\distribute\nnUNETDatav2.Head_RAM\Dataset131_Head_CT2CT_3x3x3#nnUNetTrainerNoMirroring__nnUNetPlans__3d_AE#fold_0.opt');
            FOVExtension=struct('DstSize_mm', [-1 -1 250], 'offcenterratio', -0.5, 'Overlap_mm', [0 0 10]); 
            augcfg = struct('FOVExtension', FOVExtension, 'mask_include_mode', 0,'augument_intensity', 'imagemean_noise0.1');
            augprocesses =daemon.taskdef.nnUNET.AugmentInferenceProcess(augmodeloptfile, augcfg, 'image.nii.gz', 'image'); 
            augprocesses=cat(2, augprocesses,  {ReformCSProcess});
            daemon.taskdef.qMRI.MR2SynCT(tasksdeffile, subtaskname, modeloptfiles, seqs, augprocesses, {'InferenceCfg', InferenceCfg});
            daemon.taskdef.TaskDef.TaskDef_filterMRDescriptions(tasksdeffile, ['MR_' subtaskname], seqs, {'DBTableName', 'MR'});
        end
 
        function MR2SynCTCommon(tasksdeffile)
            subtaskname='SynCTCommon'; 
            optroot = 'C:\ARTDaemon\distribute\nnUNETDatav2.unity20241130\';
            seqs    = {'T1', 'T2', 'Flair'};
            seqnames = {'T1w', 'T2w', 'T2Flair'};
            datasetnums = {'313', '323', '333'};
             modeloptfiles = cellfun(@(seq, seqname, num)([optroot 'Dataset' num '_unity20241130_' seqname '2CTm#nnUNetTrainer__nnUNetPlans__3d_fullres#fold_0.opt']), ...
                seqs,seqnames, datasetnums, 'UniformOutput',false); 
           
            daemon.taskdef.qMRI.MR2SynCT(tasksdeffile, subtaskname, modeloptfiles, seqs);
        end

        function MROP_site(tasksdeffile, site, varargin)
            options = OptionsMap(varargin{:});

            dbname = 'MR';
            MROPSite  = ['MROP' site];
            SynCTSite = ['SynCT' site];
            %RSSetName = [MROPSite];
            %RSSetName = ['SegmanMR'];

            
            %SegmanTaskFolder = options.getOption('SegmanTaskFolder'); %['../MetsSeg/Segman_MR_Head/MR.[SeriesInstanceUID].tsk']
            %segmanlabelmaskfile =options.getOption('SegmanROIMaskFile'); %segmanlabelmaskfile = "../Segman_MR_Head/labelmask.nii.gz";
            SynCTTaskFile= options.getOption('SynCTTaskFile', ['MR_' SynCTSite '2Dcm/MR.[SeriesInstanceUID].tsk']);
            taskdeffolder = [fileparts(tasksdeffile) '/'];
           
            %CustomSubFolder='MROPHead/';
            CustomSubFolder=SynCTSite;
            dcmjson ='image_dcm.json';

            
            
            m=0; Dependency=[];
            m=m+1;Dependency{m}=struct(...
	     	    "filename",     dcmjson, ...
                "taskfilename", SynCTTaskFile);
            % if ~isempty(segmanlabelmaskfile)
            %     m=m+1;Dependency{m}=struct(...
	     	%         "filename",     segmanlabelmaskfile, ...
            %         "taskfilename",SegmanTaskFile);
            % end
            
            %m=0;   Process=[];
            
            % OutputDcmRSFile = ['./dcm/' RSSetName '.dcm'];
            % if ~isempty(segmanlabelmaskfile)
            % m=m+1; Process{m}=struct('OperationType','DcmExportROIMask',"InputROIMaskImage", segmanlabelmaskfile,...
            %     "OrigImageHeaderFile", dcmjson, "OutputDcmRSFile", OutputDcmRSFile,...
            %     "RSSetName", RSSetName);
            % end
            % taskdefname=['MR_' MROPSite];
            % obj = daemon.taskdef.TaskDef_Atlas({'TaskDefFolder', taskdeffolder}, ...
            %                 {'TaskDefName', taskdefname});
            % obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', CustomSubFolder});

            taskdefname=['MR_' MROPSite '2TPS'];
            %m=0; Dependency=[];
            % m=m+1;Dependency{m}=struct(...
	     	%     "filename",     OutputDcmRSFile, ...
            %     "taskfilename", ['MR_' MROPSite '/MR.[SeriesInstanceUID].tsk']);
            %m=0;   Process=[];          
            % m=m+1; Process{m}=struct('OperationType','ExportDcm','InputDcm', OutputDcmRSFile,...
            %     "DcmExportPort","TPS");
            m=0;   Process=[];
            augmentprocess=options.getoptioni('preaugmentprocess');
            if ~isempty(augmentprocess)
                Process = cat(2,  Process, StructBase.toCell(augmentprocess)); 
                m=numel(Process);
            end

            m=m+1; Process{m}=struct('OperationType','ExportDcm','InputDcm', ['../' SynCTSite '/dcm'],...
                "DcmExportPort","TPS0|TPS");

            % if ~isempty(SegmanTaskFolder)
            %     tskfile = [SegmanTaskFolder 'MR.[SeriesInstanceUID].tsk'];
            %      m=m+1; Process{m}=struct('OperationType','CreateTaskFile','TaskFileName', tskfile );
            % end

            augmentprocess=options.getoptioni('postaugmentprocess');
            if ~isempty(augmentprocess)
                Process = cat(2,  Process, StructBase.toCell(augmentprocess)); 
                m=numel(Process);
            end

            obj = daemon.taskdef.TaskDef_Atlas({'TaskDefFolder', taskdeffolder}, ...
                            {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', CustomSubFolder}, {'IsCustomTask', 1});
        end
        
        function MROPs(tasksdeffile)
            site = 'Head'; tskfile = ['../Admin/Segman2TPS_MROPHead/MR.[SeriesInstanceUID].tsk'];
            augmentprocess=struct('OperationType','CreateTaskFile','TaskFileName', tskfile );
            daemon.taskdef.qMRI.MROP_site(tasksdeffile, site, {'preaugmentprocess', augmentprocess});
            
            site = 'HeadX'; tskfile = ['../Admin/Segman2TPS_MROPHead/MR.[SeriesInstanceUID].tsk'];
            augmentprocess=struct('OperationType','CreateTaskFile','TaskFileName', tskfile );
            daemon.taskdef.qMRI.MROP_site(tasksdeffile, site, {'preaugmentprocess', augmentprocess});


            % daemon.taskdef.qMRI.MROP_site(tasksdeffile, 'Common', {'SegmanTaskFile', ['../Atlas/MR_ts2_total_mr/MR.[SeriesInstanceUID].tsk']}, ...
            %     {'SegmanROIMaskFile',  '../ts2_total_mr/labelmask_merge.nii.gz'});
            site = 'Common'; tskfile = ['../Admin/Segman2TPS_MR_Common/MR.[SeriesInstanceUID].tsk'];
            augmentprocess=struct('OperationType','CreateTaskFile','TaskFileName', tskfile );
            daemon.taskdef.qMRI.MROP_site(tasksdeffile, site, {'preaugmentprocess', augmentprocess});

            % daemon.taskdef.qMRI.MROP_site(tasksdeffile, 'Common', {'SegmanTaskFile', ['../MetsSeg/SegmanMR_Common25_dmts2/MR.[SeriesInstanceUID].tsk']}, ...
            %      {'SegmanROIMaskFile',  '../SegmanMR_Common25_dmts2/roimask.nii.gz'});
            %Segman2TPS_MR_Common25_dmts2.taskdef
        end
    end
end