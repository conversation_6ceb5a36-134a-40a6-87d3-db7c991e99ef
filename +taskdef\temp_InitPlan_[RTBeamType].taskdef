{"Process": {"OperationType": "CalcInitPlan", "Input": {"SrcMotion": "[SrcMotionFile]", "OptMask": "[OptMaskFile]"}, "Output": {"BixelFile": "[OutBixelFile]", "FluenceFile": "[OutFluenceFile]", "SrcMotionFile": "[OutMotionFile]"}, "AppOptions": [{"OptionName": "exefile", "OptionValue": "[ProjectionExeFile]"}, {"OptionName": "in.RTBeamType", "OptionValue": "[RTBeamType]"}, {"OptionName": "in.fluence.field.x", "OptionValue": "[FieldSizeX]"}, {"OptionName": "in.fluence.field.y", "OptionValue": "[FieldSizeY]"}, {"OptionName": "in.fluence.bixel.x", "OptionValue": "[PixelSize]"}, {"OptionName": "in.fluence.bixel.y", "OptionValue": "[<PERSON><PERSON><PERSON><PERSON>]"}, {"OptionName": "in.fluence.pixel.x", "OptionValue": "[PixelSize]"}, {"OptionName": "in.fluence.pixel.y", "OptionValue": "[PixelSize]"}, {"OptionName": "out.initaper.motion.file", "OptionValue": "motion.txt"}, {"OptionName": "out.initaper.bixelmap.file", "OptionValue": "bixelmap"}, {"OptionName": "initapers.target.margin", "OptionValue": "[TargetMargin]"}, {"OptionName": "in.initaper.gantry.angles", "OptionValue": "[G<PERSON><PERSON><PERSON><PERSON>]"}, {"OptionName": "in.initaper.usetargetcenter", "OptionValue": "[UseTargetCenter]"}]}, "DefaultSettings": {"ProjectionExeFile": "C:/ARTDaemon/distribute/wtkapp/bin/appinitaper.exe", "OutMotionFile": "InitMotion.json", "OutBixelFile": "InitBixelmap", "OutFluenceFile": "InitFluencemap", "TargetMargin": "0.5", "LeafWidth": "0.5", "PixelSize": "0.1", "FieldSizeX": "40", "FieldSizeY": "40", "GantryAngle": "", "FluenceFile": "", "DensityFile": "", "SrcMotionFile": "", "UseTargetCenter": "1"}}