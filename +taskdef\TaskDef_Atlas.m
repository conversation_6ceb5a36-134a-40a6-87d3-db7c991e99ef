classdef TaskDef_Atlas<daemon.taskdef.TaskDef
    properties

    end

    methods
        function obj = TaskDef_Atlas(varargin)
            <EMAIL>(varargin{:});
            obj.SetDefaultCfg;
        end

    end

    methods(Static)
          function CreateTemplateTaskDef(tasksdeffile, taskdefname, process, tempargs, varargin)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj = daemon.taskdef.TaskDef_Atlas({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});

            [tempprocess]=obj.CreateTemplateArguments(tempargs);
            process = StructBase.mergeStruct(process, tempprocess);
            obj.Generate(tasksdeffile, [], process, varargin{:});
            % filename= TaskDefFileName(obj);
            % obj.TaskDefFile2TaskTemplate(filename);
        end

        function Factory_tempprocess(tasksdeffile)
            taskdeffolder = [fileparts(tasksdeffile) '/'];

            taskdefname='temp_ExecuteCommand';
            process = struct('OperationType', 'ExecuteCommand', "CommandType", 'cmd');
            tempargs = {'CommandFile', 'WorkingDir'};
            daemon.taskdef.TaskDef_Atlas.CreateTemplateTaskDef(tasksdeffile, ...
                taskdefname, process, tempargs,{'IsAdminTask', 1});

            % taskdefname='temp_nnUNETInference';
            % process = struct('OperationType', 'nnUNETInference');
            % tempargs = {'ModelOptionFile', 'InputImage', 'OutputFile'};
            % daemon.taskdef.TaskDef_Atlas.CreateTemplateTaskDef(tasksdeffile, ...
            %     taskdefname, process, tempargs, {'Dependency', struct("filename", '[InputImage]')});

            taskdefname='temp_RegMapImage';
            ExportImage = struct('ImageFileName', '[OutputFile]');
            process = struct('OperationType', 'RegMapImage', 'MovingImageType', 'VolHeaderImage', 'ExportImage', ExportImage);
            tempargs = {'FixedImageFileName', 'MovingImageFileName', 'RegID_Image', 'RegID_FOR', 'RegTableFile'};
            DefaultSettings = struct( 'RegID_Image','none', 'RegID_FOR','none');
            daemon.taskdef.TaskDef_Atlas.CreateTemplateTaskDef(tasksdeffile, ...
                taskdefname, process, tempargs, {'Dependency', struct("filename", '[FixedImageFileName]|[MovingImageFileName]')}, ...
                {'DefaultSettings', DefaultSettings});

            taskdefname='temp_RegMapROIMask';
            ExportImage = struct('ImageFileName', '[OutputFile]');
            process = struct('OperationType', 'RegMapImage', 'MovingImageType', 'ROIMaskImage', 'ExportImage', ExportImage);
            tempargs = {'FixedImageFileName', 'MovingImageFileName', 'RegID_Image', 'RegID_FOR', 'RegTableFile'};
            DefaultSettings = struct( 'RegID_Image','none', 'RegID_FOR','none');
            daemon.taskdef.TaskDef_Atlas.CreateTemplateTaskDef(tasksdeffile, ...
                taskdefname, process, tempargs, {'Dependency', struct("filename", '[FixedImageFileName]|[MovingImageFileName]')}, ...
                {'DefaultSettings', DefaultSettings});

            taskdefname='temp_ImportDcmPatient';
            process  = struct('OperationType', 'ImportDcmPatient');
            tempargs = {'PatientID', 'SrcDcmRoot'};
            daemon.taskdef.TaskDef_Atlas.CreateTemplateTaskDef(tasksdeffile, ...
                taskdefname, process, tempargs,{'IsAdminTask', 1}, {'TaskOutputFolder',''});

            dbnames = {'CT', 'MR', 'PT'};
            for k=1:numel(dbnames)
                dbname = dbnames{k};
                taskdefname   = [dbname '_temp_AssociateLabelMask'];
                AssociateInfo = struct("ID", '[LabelMaskID]',"LabelMaskFileName",'[LabelMaskFileName]');
                m=0; Process=[];
                m=m+1; Process{m}= struct('OperationType', 'AssociateStruct','AssociateFileName', './dcmdata.cfg', 'ListName', 'StructSet', 'Info', AssociateInfo);
                obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                            {'TaskDefName', taskdefname});
                obj.Generate(tasksdeffile, [], Process, {'DBTableName',dbname}, {'CustomSubFolder', ''}, {'IsAdminTask', 2}, {'IsCustomTask', 1});
            end

            taskdefname='temp_ExportDcm';
            InputDcm   ='[DICOM]';
            DcmExportPort='[ExportPort]';
            m=0;   Process=[];
            m=m+1; Process{m}=struct('OperationType', 'ExportDcm', 'InputDcm', InputDcm, 'DcmExportPort', DcmExportPort);
            DefaultSettings = struct('ExportPort','TPS');
            obj = daemon.taskdef.TaskDef({'TaskDefFolder', taskdeffolder}, ...
                            {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, [], Process, {'CustomSubFolder', ''}, {'IsAdminTask', 2}, {'IsCustomTask', 1}, {'DefaultSettings', DefaultSettings});

            taskdefname='temp_ROIMask2DcmRS';
            InputROIMaskImage   ='[InputROIMaskImage]';
            OutputDcmRSFile='[OutputDcmRSFile]';
            RSSetName='[RSSetName]';
            OrigImageHeaderFile='[OrigImageHeaderFile]';
            DefaultSettings = struct('RSSetName','SegmanRS');
            m=0;   Process=[];
            m=m+1; Process{m}=struct('OperationType', 'DcmExportROIMask','InputROIMaskImage', InputROIMaskImage,...
                'OutputDcmRSFile',OutputDcmRSFile, 'RSSetName', RSSetName, 'OrigImageHeaderFile', OrigImageHeaderFile);
            obj = daemon.taskdef.TaskDef_Atlas({'TaskDefFolder', taskdeffolder}, ...
                            {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, [], Process, {'DefaultSettings', DefaultSettings});
        end

        function Factory_GP(tasksdeffile)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            gtvtaskdefname= 'RS_ExtractROI_GP_GTV';
            tempMaskFolder= ['../'  gtvtaskdefname];
            atlascsfile = '../[ReferencedImageModality].[ReferencedImageSeriesUID]/nnunet_GP_Crop_1_5x1_5x1_5_CT2all/image.nii.gz';
            srcroimaskfile = '../RS.[SOPInstanceUID]/roimask.nii.gz';
            m=0; Dependency=[];
            m=m+1;Dependency{m}=struct(...
	     	    "filename",     srcroimaskfile, ...
                "taskfilename", '../DcmConverter/DCMCONVERT_RS/RS.[SOPInstanceUID].tsk');
            m=m+1;Dependency{m}=struct(...
	     	    "filename",     atlascsfile, ...
                "taskfilename", 'SEG_AIAtlas_nnunet_GP_Crop_1_5x1_5x1_5_CT2all/[ReferencedImageModality].[ReferencedImageSeriesUID].tsk');

            m=0; roiops =[];
            cfg = struct('ROIName', 'GTV', 'ROIMatchField', 'ROIName', 'ROIMatchStr', 'GTV.*', 'ROIMatchMode', 'regexp');
            m=m+1; roiops{1}=struct('OperationType', 'KeepSubROIs', 'ROIList', cfg);
            m=m+1; roiops{2}=struct('OperationType', 'ReformCS', 'AtlasCS', atlascsfile);
            
            m=0; Process=[];
            tempROIMaskImage='tempMask_GTV';
            m=m+1; Process{m} = struct('OperationType', 'ProcessROIMask','SrcROIMaskImage', srcroimaskfile,...
                'DstROIMaskFileName', tempROIMaskImage);
            Process{m}.Operation=roiops; 
            
            obj = daemon.taskdef.TaskDef_Atlas({'TaskDefFolder', taskdeffolder}, ...
                            {'TaskDefName', gtvtaskdefname});
            obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', tempMaskFolder}, {'IsCustomTask', 1});


            taskdefname='CT_Crop_1_5x1_5x1_5_CT_GTV12All';
            CustomSubFolder= [taskdefname];
            m=0; Dependency=[];
            ctfile = '../nnunet_GP_Crop_1_5x1_5x1_5_CT2all/image.nii.gz';
            tempgtvmaskfile = ['../../' gtvtaskdefname '/' tempROIMaskImage '.nii.gz'];
            m=m+1;Dependency{m}=struct(...
	     	    "filename",     ctfile);
            m=m+1;Dependency{m}=struct(...
	     	    "filename",     tempgtvmaskfile);
            m=0; Process=[];
      %             "ModelOptionFile": "C:/ARTDaemon/distribute/nnUNETDatav2.BraTS2021_GBMPreOp/Dataset203_BraTS2021_T1ce-Flair2TC-ET-ED#nnUNetTrainer__nnUNetPlans__3d_fullres_T#fold_0.opt",
      % "InputImage": "[T1ceImage]|[FlairImage]",
      % "OutputFile": "labelmask",
            ModelOptionFile=DosUtil.SimplifyPath('C:\ARTDaemon\distribute\nnUNETDatav2.GP\Dataset705_Crop_1.5x1.5x1.5_CT_GTV12All#nnUNetTrainer__nnUNetPlans__3d_fullres#fold_0.opt');
            InputImage=[ctfile '|' tempgtvmaskfile];
            OutputFile= 'labelmask'; 
            m=m+1; Process{m} = struct('OperationType', 'nnUNETInference',...
                'ModelOptionFile', ModelOptionFile,...
                'InputImage', InputImage,...
                'OutputFile', OutputFile);

            obj = daemon.taskdef.TaskDef_Atlas({'TaskDefFolder', taskdeffolder}, ...
                            {'TaskDefName', taskdefname});
            obj.Generate(tasksdeffile, Dependency, Process, {'CustomSubFolder', CustomSubFolder});
        end


        function Factory_MR2mts2(tasksdeffile)
           modelfolder = 'C:\ARTDaemon\distribute\nnUNETDatav2.ts-mr\';
           modeloptext0 = '#nnUNetTrainer__nnUNetPlans__3d_fullres#fold_0'; 
           modeloptext1 = '#nnUNetTrainerNoMirroring__nnUNetPlans__3d_fullres#fold_0'; 
           modality='MR';
           daemon.taskdef.TaskDef.nnUNETSeg1(tasksdeffile,modality, [modelfolder,  'Dataset102_LPS_1.5x1.5x1.5_'], 'mri2body', modeloptext0);
           daemon.taskdef.TaskDef.nnUNETSeg1(tasksdeffile,modality, [modelfolder,  'Dataset125_LPS_1.5x1.5x1.5_'], 'mri2vertebrae', modeloptext0);
           daemon.taskdef.TaskDef.nnUNETSeg1(tasksdeffile,modality, [modelfolder,  'Dataset205_LPS_1.5x1.5x1.5_'], 'MR2mts2_BodyTissue5', modeloptext1);
           daemon.taskdef.TaskDef.nnUNETSeg1(tasksdeffile,modality, [modelfolder,  'Dataset330_LPS_1.5x1.5x1.5_'], 'MR2mts2_BodyTissueVB30', modeloptext1);
           daemon.taskdef.TaskDef.nnUNETSeg1(tasksdeffile,modality, [modelfolder,  'Dataset355_LPS_1.5x1.5x1.5_'], 'MR2mts2_BodyTissueTotal55', modeloptext1);
           daemon.taskdef.TaskDef.nnUNETSeg1(tasksdeffile,modality, [modelfolder,  'Dataset480_LPS_1.5x1.5x1.5_'], 'MR2mts2_BodyTissueTotalVB80', modeloptext1);
           daemon.taskdef.TaskDef.nnUNETSeg1(tasksdeffile,modality, [modelfolder,  'Dataset827_LPS_1.5x1.5x1.5_'], 'MR2mts2_BodyTissueTotalGT55_merge27', modeloptext1);
           daemon.taskdef.TaskDef.nnUNETSeg1(tasksdeffile,modality, [modelfolder,  'Dataset855_LPS_1.5x1.5x1.5_'], 'MR2mts2_BodyTissueTotalGT55', modeloptext1);
        end

        function PrepareTrain_nnUNET(tasksdeffile, taskdefname, traincfg)
            if ~exist('traincfg', 'var')
                traincfg=[];
            end
            argnames = {'nnUNETVersion', 'CONFIGURATION',   'TrainClass', ...
                'nnUNETRoot', 'TaskName', 'InputChannel', 'InputModality',   ...
                'LabelFile',  'LabelName','TestMod',  ...
                'OutlierCases', ...
                'LabelImageType',  ...
                'LabelImageIntensityRef',  ...
                'extraTrainOpt'};

            [Train_nnUNET, defaultsettings]=daemon.taskdef.TaskDef.CreateTemplateArguments(argnames);
            if ~isempty(traincfg)
                fns = fieldnames(traincfg);
                for k=1:numel(fns)
                    fn = fns{k};
                    Train_nnUNET.(fn)=traincfg.(fn);
                end
            end
            Train_nnUNET.('OtherOption')=struct("custom_spacing", '[custom_spacing]');
            db=struct('Excel', struct('FileName','[TrainDataXlsFileName]', 'SheetName', '[TrainDataXlsSheetName]')); 
            
            defaultsettings=rmfield(defaultsettings, {'InputChannel', 'InputModality', 'LabelFile', 'LabelImageType'});
            
            defaultsettings.('nnUNETVersion')='v2';
            defaultsettings.('CONFIGURATION')='3d_fullres';
            defaultsettings.('TrainClass')= 'nnUNetTrainerNoMirroring';
            defaultsettings.('TrainDataXlsSheetName')='';
            defaultsettings.('custom_spacing')='';
           
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            obj = daemon.taskdef.TaskDef_Atlas({'TaskDefFolder', taskdeffolder}, ...
                            {'TaskDefName', taskdefname});
            % Info = struct('DataBase', db, 'Train_nnUNET', Train_nnUNET); 
            % tskfilename = 'PrepareTrain/PrepareTrain-nnunet.tsk';
            % Process =struct('OperationType', 'CreateTskInfo','FileName', tskfilename, 'Info', Info); 
            % obj.Generate(tasksdeffile, [], Process, {'DefaultSettings', defaultsettings}, {'IsCustomTask', 1}, {'IsAdminTask', 1});
            obj.Generate(tasksdeffile, [], [],{'DataBase', db}, {'Train_nnUNET', Train_nnUNET}, {'DefaultSettings', defaultsettings}, {'IsCustomTask', 1}, {'IsAdminTask', 1});
        end
    end
end