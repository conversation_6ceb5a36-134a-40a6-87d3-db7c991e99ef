classdef RtAdmin < daemon.taskdef.TaskDef
    properties
        
    end

    methods
        function obj = RtAdmin(varargin)         
            <EMAIL>(varargin{:});
        end
    end

    methods (Static)
        function Factory_file(modeloptfolder,tasksdeffile)
            res = rdir([modeloptfolder 'Dataset*.opt']);
            fnames = {res(:).name};
            for k=1:numel(fnames)
                modeloptfile=fnames{k};
                daemon.taskdef.RtAdmin.CreateTaskDef(modeloptfile, tasksdeffile)
            end
        end
    end
end