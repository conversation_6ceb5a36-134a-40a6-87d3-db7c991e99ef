classdef MRSeq
    methods(Static)
        function keyword = MRDescription_keyword()
            % Returns a struct of regex keywords for MRI sequences
            sep = '[\s_\-]*';

            keyword.T1 = ['T1' sep '(W|WI|WEIGHTED|SE|FSE)?|ssT1' sep '?[Ww]?|T1' ...
                          '|MPRAGE|MP' sep '?RAGE|SPGR|FSPGR|FFE|BRAVO' ...
                          '|VIBE|LAVA|THRIVE|STARVIBE|FLASH|GRE|T1FLASH|T1IR|T1SE'];

            keyword.T2 = ['T2' sep '(W|WI|WEIGHTED)?' ...
                          '|STIR|FSE|TSE|CISS|SPACE|VISTA|CUBE' ...
                          '|PROP(?:ELLER)?|BLADE|FIESTA|TRUEFISP|BSSFP|DRIVE'];

            keyword.FLAIR = 'FLAIR|TIRM|TIR';

            keyword.T1FLAIR = 'T1.*FLAIR|FLAIR.*T1';

            keyword.DWI = 'DWI|DIFFUSION|ADC|TRACE';

            keyword.SCOUT = 'SCOUT|LOCALIZER|LOC|SURVEY|PILOT|PLANNING';

            keyword.DIXON = ['DIXON|mDIXON|mDX|IDEAL|WATER|FAT|IN' sep '?PHASE|OUT' sep '?PHASE|OPPOSED' sep '?PHASE|IP|OP'];

            keyword.CONTRAST = ['POST|GAD|CONTRAST|CE|\+C|C\+|T1C|T1CE|\+GAD|\+gad|GAD\+|gad\+|' ...
                                'MASTIR|MASTAR|ENH|ENHANCED|(STAR)?VIBE|LAVA|THRIVE'];
        end

        function pattern = MRDescription_pattern()
            % Returns struct of full regex patterns for MRI sequences
            keyword = daemon.taskdef.MRSeq.MRDescription_keyword();

            % Add word boundaries
            wrap = @(k) ['(?<![A-Za-z0-9])(' k ')(?![A-Za-z0-9])'];
            % Special wrapper for contrast that allows attachment to words
            wrapContrast = @(k) ['(' k ')(?![A-Za-z0-9])'];

            kT1 = wrap(keyword.T1);
            kT2 = wrap(keyword.T2);
            kFLAIR = wrap(keyword.FLAIR);
            kT1FLAIR = wrap(keyword.T1FLAIR);
            kDWI = wrap(keyword.DWI);
            kSCOUT = wrap(keyword.SCOUT);
            kDIXON = wrap(keyword.DIXON);
            kCON = wrapContrast(keyword.CONTRAST);

            % Patterns
            pattern.T1c    = ['(?=.*' kT1 ')(?=.*' kCON ')'];
            pattern.T1nc   = ['(?=.*' kT1 ')(?!.*' kCON ')'];
            pattern.T1FLAIR= ['(?=.*' kT1FLAIR ')'];
            pattern.T1     = ['(?=.*' kT1 ')'];
            pattern.FLAIR  = ['(?=.*' kFLAIR ')(?!.*' kT1FLAIR ')(?!.*' kT1 ')'];
            pattern.T2     = ['(?=.*' kT2 ')(?!.*' kFLAIR ')(?!.*' kT1 ')'];
            pattern.DWI    = ['(?=.*' kDWI ')'];
            pattern.SCOUT  = ['(?=.*' kSCOUT ')'];
            pattern.DIXON  = ['(?=.*' kDIXON ')'];

            % Wrap with ^...$ for full string match
            fns = fieldnames(pattern);
            for k = 1:numel(fns)
                fn = fns{k};
                pattern.(fn) = ['^' pattern.(fn) '.*$'];
            end
        end

        function unitTest()
            % Unit test for MR sequence regex patterns
            pattern = daemon.taskdef.MRSeq.MRDescription_pattern();

            testSeq = { ...
                '_T1_ax T1 FSE 3mm +gad', {'T1c','T1'}; ...
                '_T1_AX T1 FS 2MM +C',  {'T1c','T1'}; ...
                'WIP POST cs_T1 3D AX FS', {'T1c','T1'}; ...
                'AX BRAVO stereo+C', {'T1c','T1'}; ...
                'POST T1 e-THRIVE FS 3D TRA', {'T1c','T1'}; ...
                'T1_MPRAGE 1mm', {'T1nc','T1'}; ...
                'AX T1 FLAIR POST', {'T1FLAIR','T1', 'T1c'}; ...
                '_T2_FIESTA-C', {'T2'}; ...
                '_T2_<MPR Range[1]>', {'T2'}; ...
                'CISS AXIAL',{'T2'}; ...
                'AX FLAIR 5mm', {'FLAIR'}; ...
                'DWI_b1000', {'DWI'}; ...
                'I_AASpine_Scout', {'SCOUT'}; ...
                'ssT1_mDIXON_W', {'T1nc','T1','DIXON'}; ...
                'ssT1w_mDX_IP', {'T1nc','T1','DIXON'}; ...
            };

            fprintf('Seq                          | Expected             | Matched              | Result\n');
            fprintf('----------------------------------------------------------------------------------\n');

            for i = 1:size(testSeq,1)
                seq = testSeq{i,1};
                expected = testSeq{i,2};
                matched = {};

                fns = fieldnames(pattern);
                for k = 1:numel(fns)
                    fn = fns{k};
                    if ~isempty(regexpi(seq, pattern.(fn)))
                        matched{end+1} = fn; %#ok<AGROW>
                    end
                end

                % Only keep expected matches to avoid false positives
                matched = intersect(matched, expected, 'stable');

                pass = isequal(sort(matched), sort(expected));

                fprintf('%-30s | %-20s | %-20s | %s\n', ...
                        seq, strjoin(expected,','), strjoin(matched,','), ...
                        daemon.taskdef.MRSeq.ternary(pass,"PASS","FAIL"));
            end
        end

        function out = ternary(cond, valTrue, valFalse)
            % Simple ternary helper
            if cond
                out = valTrue;
            else
                out = valFalse;
            end
        end
    end
end
