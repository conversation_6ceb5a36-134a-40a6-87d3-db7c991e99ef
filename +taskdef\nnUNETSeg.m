classdef nnUNETSeg < daemon.taskdef.nnUNET
    properties
        
    end

    methods
        function obj = nnUNETSeg(varargin)
            options = OptionsMap({'OutputFile', 'labelmask'}, varargin{:});
            <EMAIL>(options);
            % modelid = options.getOption('ModelID');
            % defaultcfg = struct(  "DBTableName", "MR",...
            %   "InputImageFileName", "../nnUNET_Brain/image_brain.nii.gz",...
            %   "CustomSubFolder", ['MNI/' modelid]);
            % obj.SetDef_struct(defaultcfg);
            
        end
    end
end