classdef DcmRTAdminServer < daemon.DcmRTServer
    properties
        
    end
    
    methods
        function obj = DcmRTAdminServer(varargin)
            <EMAIL>(varargin{:});
        end
        
        function status = CalcAdminTask(self, task, info, varargin)
            status  = <EMAIL>(task, info, varargin{:});
            users   = task.GetTaskConfig('UpdateUser');
            if ~isempty(users)
                if isstruct(users)
                    users = arrayfun(@(x)(x), users, 'uniformoutput', false); 
                end
                for k=1:numel(users)
                    try
                        self.m_DB.UpdateUserRecord(users{k});
                    catch
                    end
                end
            end
        end
    end
end

