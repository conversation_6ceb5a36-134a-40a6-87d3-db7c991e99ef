classdef AdminTasksDef <utils.json.TasksDef    
    properties
        m_TempDef; 
    end
    %UserGroup: Service|Admin|User|Guest
    methods
        function obj = AdminTasksDef(varargin)
            <EMAIL>(varargin{:});
        end
        
        function res = PublicRootFolder(self)
            str = 'PublicRootFolder';
            if ~self.isOption(str)
                [folder] = [fileparts(self.m_cfgfile) '\'];
                res = DosUtil.SimplifyPath([folder '..\..\']); 
                self.setOption(str, res); 
            else
                res = self.getOption(str); 
            end
        end
        
        function res = FrontEndConfigFolder(self)
           str = 'FrontEndConfigFolder';
           if ~self.isOption(str)
                res = [PublicRootFolder(self) 'ServiceWeb/public/']; 
                self.setOption(str, res); 
           else
                res = self.getOption(str); 
           end
        end
        
        function AddTaskDef(obj,taskname, posttaskfiles, varargin)
            info = obj.CreateTaskDef( posttaskfiles, varargin{:});
            if ~isempty(taskname)
                info.TaskName = taskname; 
            end
            %obj.m_cfg.(taskname)=info; 
            if isempty(obj.m_TempDef)
                obj.m_TempDef={info};
            else
                N = numel(obj.m_TempDef);
                obj.m_TempDef{N+1}=info; 
            end
        end
        
        function AddTasks_User(obj)
            AddTaskDef(obj, 'UpdateUser',   []);
    
            dbname    = 'USER';
            usergroup = 'Service';
            posttaskfiles='';
            dcmlevel  = 'Service';
            
            shortname = 'NewUser';
            taskname  = shortname;
            templatetask   = ['Admin/' taskname '/[UserID].tsk'];
            templatetaskdef= struct('UserInfo', '<EditBox>');
            editbox=struct('UserID','', 'Password','', 'LastName','', 'FirstName','',...
                'AccountRoles', 'User','AccountStatus', 'Active'); 
            AddTaskDef(obj,taskname,   posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                    {'TemplateTaskFile', templatetask}, {'EditBox', editbox}, {'DBTableName', dbname}, {'TemplateTaskDef', templatetaskdef});
            
            shortname = 'EditUser';
            taskname  = shortname;
            templatetask   = ['Admin/' taskname '/[UserID].tsk'];
            templatetaskdef= struct('UserInfo', '<EditBox>');
            editbox=struct('UserID','[UserID]', 'Password','', 'LastName','[LastName]', 'FirstName','[FirstName]',...
                'AccountRoles', '[AccountRoles]','AccountStatus', '[AccountStatus]'); 
            AddTaskDef(obj,taskname,   posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                    {'TemplateTaskFile', templatetask}, {'EditBox', editbox}, {'DBTableName', dbname}, {'TemplateTaskDef', templatetaskdef});
        end
        
        function AddTasks_DcmRemove(obj)
            dbname = 'Patient'; 
            taskname = 'DcmRemove_Patient';
            usergroup='Admin'; 
            posttaskfiles={'../DataBase/removedcm/Patient.[PatientID].tsk',...
                           './RemoveFolder_Patient/[PatientID].tsk'};
            shortname = 'DelPatient';
            dcmlevel  = 'Patient';
            templatetask =['Admin/' taskname '/Patient.[PatientID].tsk'];
            AddTaskDef(obj,taskname,  posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbname}, {'Confirmation', 'Delete Patient?'});
            
            dbnames = {'RTSTRUCT', 'RTPLANDB', 'RTDOSE', 'REG'};
            modalities = {'RTSTRUCT', 'RTPLAN', 'RTDOSE', 'REG'};
            posttaskfiles='../DataBase/removedcm/[Modality].[SeriesInstanceUID].tsk';
            for k=1:numel(dbnames)
                modality = modalities{k};
                shortname = ['DelSeries'];
                taskname = ['DcmRemove_' modality];
                dcmlevel=modality; 
                templatetask =['Admin/' taskname '/[Modality].[SeriesInstanceUID].tsk'];
                AddTaskDef(obj,taskname,  posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbnames{k}}, {'Confirmation', 'Delete Series?'});
            end
            
            dbnames = {'CT', 'MR', 'PT'};
            modalities = {'CT', 'MR', 'PT'};
            for k=1:numel(dbnames)
                modality = modalities{k};
                shortname = ['Del' modality];
                taskname = ['DcmRemove_' modality];
                posttaskfiles={ ['../DataBase/removedcm/'  modality '.[SeriesInstanceUID].tsk'], ...
                                ['./RemoveFolder_Patient/[PatientID]#' modality '.[SeriesInstanceUID].tsk']};
                dcmlevel=modality; 
                templatetask =['Admin/' taskname '/[Modality].[SeriesInstanceUID].tsk'];
                AddTaskDef(obj,taskname,  posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbnames{k}}, {'Confirmation', 'Delete Series?'});
            end

            dbnames = {'RTSTRUCT', 'RTPLANDB', 'RTDOSE', 'REG'};
            modalities = {'RS', 'RP', 'RD', 'RE'};
            for k=1:numel(dbnames)
                modality  = modalities{k};
                shortname = ['Del' modality];
                taskname  = ['DcmRemove_' modality];
                dcmlevel=modality; 
                templatetask =['Admin/' taskname '/' modality '.[SOPInstanceUID].tsk'];
                posttaskfiles={ ['../DataBase/removedcm/'  modality '.[SOPInstanceUID].tsk'], ...
                                ['./RemoveFolder_Patient/[PatientID]#' modality '.[SOPInstanceUID].tsk']};
                AddTaskDef(obj,taskname,  posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbnames{k}}, {'Confirmation', 'Delete Series?'});
            end
        end
        
        function AddTasks_DcmAnon(obj)
            dbname = 'Patient'; 
            taskname = 'DcmAnon_Patient';
            usergroup='User'; 
            posttaskfiles='../DataBase/dcmanon/[PatientID].tsk';
            shortname = 'AnonPatient';
            dcmlevel = 'Patient';
            templatetask =['Admin/' taskname '/' 'Patient.[PatientID].tsk'];
            AddTaskDef(obj,taskname,  posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbname});
            
            dbnames = {'CT', 'MR', 'PT'};
            modalities = {'CT', 'MR', 'PT'};
            posttaskfiles='../DataBase/dcmanon/[PatientID]#[StudyInstanceUID].tsk';
            shortname = 'AnonStudy';
            for k=1:numel(dbnames)
                modality = modalities{k};
                taskname = ['DcmAnon_' modality '_Study'];
                dcmlevel=modality; 
                templatetask =['Admin/' taskname '/[Modality].[SeriesInstanceUID].tsk'];
                AddTaskDef(obj,taskname,  posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', ''},...
                    {'TemplateTaskFile', templatetask}, {'DBTableName', dbnames{k}});
            end
            
            dbnames    = {'CT', 'MR', 'PT', 'RTSTRUCT', 'RTPLANDB', 'RTDOSE', 'REG'};
            modalities = {'CT', 'MR', 'PT', 'RTSTRUCT', 'RTPLAN', 'RTDOSE', 'REG'};
            posttaskfiles='../DataBase/dcmanon/[PatientID]#[StudyInstanceUID]#[Modality].[SeriesInstanceUID].tsk';
            shortname = 'AnonSeries';
            for k=1:numel(dbnames)
                modality = modalities{k};
                taskname = ['DcmAnon_' modality];
                dcmlevel=modality; 
                templatetask =['Admin/' taskname '/[Modality].[SeriesInstanceUID].tsk'];
                AddTaskDef(obj,taskname,   posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', ''},...
                    {'TemplateTaskFile', templatetask}, {'DBTableName', dbnames{k}});
            end
        end
        
        function AddTasks_DcmConvert(obj)         
            dbnames    = {'CT', 'MR', 'PT', 'RTSTRUCT', 'RTPLANDB', 'RTDOSE', 'REG'};
            modalities = {'CT', 'MR', 'PT', 'RS', 'RP', 'RD', 'RE'};
            uidtypes = {'SeriesInstanceUID','SeriesInstanceUID', 'SeriesInstanceUID', 'SOPInstanceUID', 'SOPInstanceUID', 'SOPInstanceUID', 'SOPInstanceUID'};  
            usergroup='User'; 
            for k=1:numel(dbnames)
                modality = modalities{k};
                taskname = '';  posttaskfiles='';
                shortname = ['Convert' modality];
                dcmlevel  = modality; 
                templatetask=['DcmConverter/DCMCONVERT_' modality '/' modality '.[' uidtypes{k} '].tsk'];
                AddTaskDef(obj,taskname,   posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                    {'TemplateTaskFile', templatetask}, {'DBTableName', dbnames{k}});
            end
        end
        
        function AddTasks_RemoveFile(obj)            
            taskname = 'RemoveFile_Task';
            posttaskfiles='./ListFile_TasksInQeue/EVERYONE.tsk';
            AddTaskDef(obj,taskname,   posttaskfiles,{'RemoveFileName' '*.tsk'},...
                {'RootFolder', '../Tasks/'});
            
            taskname = 'RemoveFile_StausErr';
            posttaskfiles='./ListFile_StatusErr/EVERYONE.tsk';
            AddTaskDef(obj,taskname,   posttaskfiles,{'RemoveFileName' 'status.err'});
            
            taskname = 'RemoveFile_StausLock';
            posttaskfiles='./ListFile_StatusLock/EVERYONE.tsk';
            AddTaskDef(obj,taskname,   posttaskfiles,{'RemoveFileName' 'status.lock'});

            usergroup = 'Admin';
            dcmlevel = 'DcmRoot';
            dbname   = '';
           
            taskname = 'ClearLock_All'; 
            templatetask =['Admin/' taskname '/EVERYONE.tsk'];
            posttaskfiles='./RemoveFile_StausLock/EVERYONE.tsk';
            shortname = 'ClearLock';
            AddTaskDef(obj,taskname,   posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbname}, {'Credential', 'login'});
            
            taskname = 'ClearErr_All'; 
            templatetask =['Admin/' taskname '/EVERYONE.tsk'];
            posttaskfiles='./RemoveFile_StausErr/EVERYONE.tsk';
            shortname = 'ClearErr';
            AddTaskDef(obj,taskname,  posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'Credential', 'login'});
            
            taskname = 'ClearTask_All'; 
            templatetask =['Admin/' taskname '/EVERYONE.tsk'];
            posttaskfiles='./RemoveFile_Task/EVERYONE.tsk';
            shortname = 'ClearTask';
            AddTaskDef(obj,taskname,  posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbname}, {'Credential', 'login'});
           
            dcmlevel = 'Patient_Lock';
            dbname   = 'Patient';
            taskname = 'ClearLock_Patient'; 
            templatetask =['Admin/' taskname '/Patient.[PatientID].tsk'];
            posttaskfiles='./RemoveFile_StausLock/[PatientID].tsk';
            shortname = 'ClearLock';
            AddTaskDef(obj,taskname,  posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbname}, {'Credential', 'login'});
            
            dcmlevel = 'Patient_Err';
            dbname   = 'Patient';
            taskname = 'ClearErr_Patient'; 
            templatetask =['Admin/' taskname '/Patient.[PatientID].tsk'];
            posttaskfiles='./RemoveFile_StausErr/[PatientID].tsk';
            shortname = 'ClearErr';
            AddTaskDef(obj,taskname,posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbname}, {'Credential', 'login'});
        end
        
        function AddTasks_ExportDB(obj)
            posttaskfiles=[];
            dbnames    = {'CT', 'MR', 'PT', 'RTSTRUCT', 'RTPLANDB', 'RTDOSE', 'REG'};
            modalities = {'CT', 'MR', 'PT', 'RS', 'RP', 'RD', 'RE'};
            for k=1:numel(dbnames)
                taskname = ['QueryDBTable_' modalities{k}];
                % AddTaskDef(obj,taskname,   posttaskfiles,{'DBTable' dbnames{k}},...
                %     {'OutputXlsFile', '../Outputs/RTPlanDB.xlsx'});
                AddTaskDef(obj,taskname,   posttaskfiles,{'DBTable' dbnames{k}});
            end
            
            posttaskfiles = cellfun(@(x)(['QueryDBTable_' x '/EVERYONE.tsk']), modalities, 'uniformoutput', false);
            taskname  = 'ExportDB_All';
            shortname = 'ExportDB';
%             templatetask =['Admin/' taskname  '/USER.[UserID].tsk'];
%             templatetaskdef =struct('UserID', 'UserID');
%             dbname    = 'USER';
            templatetask =['Admin/' taskname  '/EVERYONE.tsk'];
            templatetaskdef =struct('UserID', 'UserID');
            dbname    = '';
            usergroup = 'User';
            dcmlevel  = 'DcmRoot';

            AddTaskDef(obj,taskname,posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'Credential', 'login'});
        end
        
        function AddTasks_CheckStatus(obj)
            posttaskfiles=[];
            rootfolders={'./', './', './'};
            filepats   ={'**/status.err', '**/status.lock', '../Tasks/*/*.tsk'};
            names = {'StatusErr', 'StatusLock', 'TasksInQueue'};
            tasknames = cellfun(@(x)(['ListFile_' x]), names, 'uniformoutput', false);
            for k=1:numel(names)
                name = names{k};
                taskname = tasknames{k};
                AddTaskDef(obj,taskname,   posttaskfiles,...
                    {'FilePattern',  filepats{k}},  ...
                    {'RootFolder', rootfolders{k}}, ...
                    {'OutputJsonFile', ['../Outputs/' name '.json']});
            end
            usergroup='User'; 
            dcmlevel ='DcmRoot';
            taskname ='CheckStatus';
            shortname='RefreshStatus';
            templatetask=['Admin/' taskname  '/EVERYONE.tsk'];
            posttaskfiles=cellfun(@(x)([x '/EVERYONE.tsk']), tasknames, 'uniformoutput', false);
            AddTaskDef(obj,taskname,posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask});
            
            taskname = 'ScheduleCheckStatus_1min';
            schedule = struct('RecruringPeriod', 1, 'RecruringPeriodUnit', 'minute');
            posttaskfiles='CheckStatus/EVERYONE.tsk';
            AddTaskDef(obj,taskname,posttaskfiles, {'Schedule', schedule});
        end
        
        
        
        function AddTasks_Archive(obj)
            taskname = 'ScheduleCleanTempFolder';
            posttaskfiles=[];
            schedule = struct('RecruringPeriod', 1, 'RecruringPeriodUnit', 'month');
            archive  = struct('ArchiveMode', 'delete', 'FolderIdleDays', 30, ...
                'SrcFolder', '../DataBase/nifti/');
            AddTaskDef(obj,taskname,posttaskfiles, {'Schedule', schedule}, {'Archive', archive});

            taskname = 'ScheduleArchiveDcm';
            posttaskfiles=[];
            schedule = struct('RecruringPeriod', 1, 'RecruringPeriodUnit', 'month');
            archive  = struct('ArchiveMode', 'movewithlink', 'FolderIdleDays', 30, ...
                'SrcFolder', '../DataBase/DicomData/', 'DstFolder', '../DicomArchive/');
            AddTaskDef(obj,taskname,posttaskfiles, {'Schedule', schedule}, {'Archive', archive});
        end
        
        function AddTasks_Refresh(obj)
            posttaskfiles=[];
            taskname = ['RemoveFolder_Patient'];
            AddTaskDef(obj,taskname,   posttaskfiles,...
                    {'RemoveFolderName', '*'});
                
            dbnames    = {'CT', 'MR', 'PT', 'RTSTRUCT', 'RTPLANDB', 'RTDOSE', 'REG'};
            modalities = {'CT', 'MR', 'PT', 'RS', 'RP', 'RD', 'RE'};
            for k=1:numel(modalities)
                modality = modalities{k};
                taskname = ['RemoveFolder_' modality ];
                AddTaskDef(obj,taskname,   posttaskfiles,{'DBTable' dbnames{k}},...
                    {'RemoveFolderName', [modality '.*']})
            end
            
            taskname = ['Refresh_Patient'];
            posttaskfiles = 'RemoveFolder_Patient/[PatientID].tsk';
            shortname = 'Refresh';
            templatetask =['Admin/' taskname  '/Patient.[PatientID].tsk'];
            usergroup='User'; 
            dcmlevel = 'Patient';
            dbname   = 'Patient';
            AddTaskDef(obj,taskname,posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbname});
        end
        
        function AddTasks_RegMap(obj)
            modalities = {'CT', 'MR'};
            usergroup  = 'User'; 
            shortname  = 'MapRS'; 
            posttaskfiles=[];
            
            for k=1:numel(modalities)
                modality = modalities{k};
                taskname = ['RegMapRS_2' modality];
                dbname  = modality;
                dbname2 = 'RTSTRUCT';
                dcmlevel  = modality;
                Filter = struct('PatientID', '[PatientID]'); 
                SelectionBox = struct('Header', 'Select Item', 'DBTableName', dbname2, 'Filter', Filter, 'ItemText', 'RS[StructureSetDate]');
                templatetask =['DcmConverter/' taskname  '/' modality '.[SeriesInstanceUID]#RS.{SOPInstanceUID}.tsk'];
                AddTaskDef(obj,'',posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', templatetask}, {'DBTableName', dbname},{'DBTableName2', dbname2}, {'SelectionBox', SelectionBox});
            end
            
            for k=1:numel(modalities)
                modality1 = modalities{k};
                for m=1:numel(modalities)
                    modality2 = modalities{m};
                    shortname = ['Map' modality2];
                    taskname = ['RegFor_' modality2 '2' modality1];
                    dbname  = modality1;
                    dbname2 = modality2;
                    dcmlevel  = modality1;
                    Filter = struct('PatientID', '[PatientID]'); 
                    SelectionBox = struct('Header', 'Select Item', 'DBTableName', dbname2, 'Filter', Filter, 'ItemText', [modality2 '[StudyDate]']);
                    templatetask =['DcmConverter/' taskname  '/' modality1 '.[SeriesInstanceUID]#' modality2 '.{SeriesInstanceUID}.tsk'];
                    AddTaskDef(obj,'',posttaskfiles, {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                    {'TemplateTaskFile', templatetask}, {'DBTableName', dbname},{'DBTableName2', dbname2}, {'SelectionBox', SelectionBox});
                end
            end
        end
        
        function AddTasks_Segman(obj, modality, exportport, sites)
            %modality  = 'CT'; 
            %dcmlevel =[modality '_SegExport'];
            %sites     = {'Common', 'Brain', 'HeadNeck', 'Chest', 'Abdomen', 'Breast', 'PelvisF', 'PelvisM', 'Cardiac', 'TandO'};
            %exportport= 'Eclipse';
            posttaskfiles=[];
            usergroup='User';
            dbname=modality; 
            dcmlevel=modality; 
            for k=1:numel(sites)
                site = sites{k};
                segtaskname = ['Segman_' modality '_' site];
                %dcmtaskname = [segtaskname '_2' exportport];
                dcmtaskname = ['Segman2' exportport '_' modality '_' site];
                segoutfolder= ['../' segtaskname '/'];
                rsfile      = [segoutfolder 'RS_Segman.dcm'];
                segtaskfile = ['MetsSeg/' segtaskname '/'  modality '.[SeriesInstanceUID].tsk'];
                Dependency  = struct('filename', rsfile,...
                    'taskfilename', ['../' segtaskfile]);
                DcmExportFile=struct('DcmFileName', rsfile, 'DcmExportPort', exportport);
                shortname=segtaskname;
                AddTaskDef(obj,dcmtaskname,posttaskfiles, ...
                    {'ShortName', shortname}, {'UserGroup', usergroup}, ...
                    {'DcmLevel', ''},...
                    {'Dependency', Dependency}, {'DBTableName', modality},...
                    {'DcmExportFile', DcmExportFile}, ...
                    {'IsAdminTask', 0}, {'IsCustomTask', 1}, {'CustomSubFolder', dcmtaskname}, ...
                    {'TaskDoneIndicator',  'SegmanDcm.tsk'});
                
                shortname = ['Seg' site];
                AddTaskDef(obj,'',[], {'ShortName', shortname}, {'UserGroup', usergroup}, {'DcmLevel', dcmlevel},...
                {'TemplateTaskFile', segtaskfile}, {'DBTableName', dbname}, {'Confirmation', [segtaskname '?']});
            end
        end
        
        function list = GetTaskList_dcmlevel(obj, dcmlevel)
            %fns = fieldnames(obj.m_cfg);
            m=0; list =[];
            for k=1:numel(obj.m_TempDef)
                %fn = fns{k};
                %cfg = obj.m_cfg.(fn);
                cfg = obj.m_TempDef{k};
                if strcmpi(dcmlevel, StructBase.getfieldx(cfg, 'DcmLevel'))
                    m=m+1;
                    temp=struct('ShortName', '', 'DBTableName', '', 'TemplateTaskFile', '', 'UserGroup', ''); 
                    temp = StructBase.copyStructFields(temp, cfg); 
                    
                    optfns = {'TemplateTaskDef', 'DBTableName2', 'Confirmation', 'Credential', 'SelectionBox', 'EditBox'};
                    for kk=1:numel(optfns)
                        fn = optfns{kk};
                        if isfield(cfg, fn)
                            temp.(fn)=cfg.(fn); 
                        end
                    end
                   
                    list{m} = temp; 
                end
            end
        end
        
        function cfg = ExportBackEnd(obj)
            frontfns = {'TaskName', 'Confirmation', 'ShortName', 'TemplateTaskFile','TemplateTaskDef', 'UserGroup', 'DcmLevel', 'Credential'}; 
            for k=1:numel(obj.m_TempDef)
                def = obj.m_TempDef{k};
                taskname = StructBase.getfieldx(def, 'TaskName'); 
                if ~isempty(taskname)
                    fns = intersect(fieldnames(def), frontfns); 
                    cfg.(taskname) = rmfield(def, fns); 
                end
            end
        end
        
        function cfg = ExportFronEnd(obj, dcmlevels)
            if ~exist('dcmlevels', 'var')
                dcmlevels = {'DcmRoot', 'Patient', 'CT', 'MR', 'PT', 'RS', 'RP', 'RD', 'RE', 'Patient_Err', 'Patient_Lock', 'Service'};
            end
            
            cfg.DataBaseFile='DataBase/RTPlanDB.sqlite';
            cfg.TasksFolder ='Tasks/';
            cfg.TasksOutputFolder='Outputs/';
            cfg.TasksHistoryFolder='TasksHistory/';
            cfg.OutputDataRoot='dcm2nifti/';
            cfg.LogsFolder='Logs/';
            cfg.StatusErrListFile='Outputs/StatusErr.json';
            cfg.StatusLockListFile='Outputs/StatusLock.json';
            cfg.TasksInQueueListFile='Outputs/TasksInQueue.json';
            
            %dashboard.Patient.DataBase
            db.Patient.DataBase=struct('DBTableName', 'PATIENT', 'PrimaryKey', 'PatientID');
            db.Course.DataBase =struct('DBTableName', 'COURSE', 'PrimaryKey', 'CourseID');
            db.CT.DataBase=struct('DBTableName', 'CT', 'PrimaryKey', 'SeriesInstanceUID');
            db.MR.DataBase=struct('DBTableName', 'MR', 'PrimaryKey', 'SeriesInstanceUID');
            db.PT.DataBase=struct('DBTableName', 'PT', 'PrimaryKey', 'SeriesInstanceUID');
            db.RP.DataBase=struct('DBTableName', 'RTPLANDB', 'PrimaryKey', 'SOPInstanceUID');
            db.RS.DataBase=struct('DBTableName', 'RTSTRUCT', 'PrimaryKey', 'SOPInstanceUID');
            db.RD.DataBase=struct('DBTableName', 'RTDOSE', 'PrimaryKey', 'SOPInstanceUID');
            db.RE.DataBase=struct('DBTableName', 'REG', 'PrimaryKey', 'SOPInstanceUID');
            db.User.DataBase=struct('DBTableName', 'USER', 'PrimaryKey', 'UserID');
            %cfg.DataBase=DB; 
            
            db.Patient.Tag = {'[PatientName]', '[PatientID]'};
            db.CT.Tag = {'CT[StudyDate]'};
            db.MR.Tag = {'MR[StudyDate]'};
            db.PT.Tag = {'PT[StudyDate]'};
            db.RP.Tag = {'RP[RTPlanDate]'};
            db.RS.Tag = {'RS[StructureSetDate]'};
            db.RD.Tag = {'RD[SeriesDate]'};
            db.RE.Tag = {'RE[ContentDate]'};
            %Tips.DcmRoot={};
            db.Patient.Tips={'[Status]', '[Comments]'};
            db.Course.Tips={'[Status]', '[Comments]'};
            db.CT.Tips={'[ManufacturerModelName]', '[SeriesInstanceUID]', '[StudyDate]', '[SeriesDescription]', '[PatientPosition]', '[SliceThickness]mm', '[NumberOfSlices]'};
            db.MR.Tips={'[ManufacturerModelName]', '[SeriesInstanceUID]', '[StudyDate]', '[SeriesDescription]', '[PatientPosition]', '[SliceThickness]mm', '[NumberOfSlices]', '[BodyPartExamined]'};
            db.PT.Tips={'[ManufacturerModelName]', '[SeriesInstanceUID]', '[StudyDate]', '[SeriesDescription]', '[PatientPosition]', '[SliceThickness]mm', '[NumberOfSlices]', '[BodyPartExamined]'};
            db.RP.Tips={'[ManufacturerModelName]', '[SOPInstanceUID]', '[RTPlanDate]', '[RTPlanLabel]', '[RTPlanDescription]', '[ApprovalStatus]', '[TreatmentMachineName]', '[StructureSetLabel]'};
            db.RS.Tips={'[ManufacturerModelName]', '[SOPInstanceUID]', '[StructureSetDate]', '[StructureSetLabel]', '[ReferencedImageModality].[ReferencedImageSeriesUID]'};
            db.RD.Tips={'[ManufacturerModelName]', '[SOPInstanceUID]', '[SeriesDate]', 'RP.[ReferencedRTPlanUID]'};
            db.RE.Tips={'[ManufacturerModelName]', '[SOPInstanceUID]', '[ContentDate]', 'Fixed: [ReferencedImageModality1].[ReferencedSeriesUID1]', 'Moving: [ReferencedImageModality2].[ReferencedSeriesUID2]', 'DVF: [DVF]'};

            for k=1:numel(dcmlevels)
                dcmlevel=dcmlevels{k};
                db.(dcmlevel).Tasks=GetTaskList_dcmlevel(obj, dcmlevel);
            end
            
            cfg.DashBoard = db;
            %fname = [obj.m_cfgfile '.frontend'];
            fname = [obj.FrontEndConfigFolder 'FrontEnd.def']; 
            utils.json.writeJson(cfg, fname);
        end
    end
    
    methods (Static)
        function obj = Factory(folder)
            obj=daemon.AdminTasksDef([folder 'tasks.def']);
            AddTasks_DcmConvert(obj);  
            AddTasks_RemoveFile(obj)
            AddTasks_DcmRemove(obj);
            AddTasks_DcmAnon(obj);
            AddTasks_ExportDB(obj);
            AddTasks_CheckStatus(obj);
            AddTasks_Refresh(obj);
            AddTasks_Archive(obj);
            AddTasks_RegMap(obj);
            AddTasks_User(obj);
            
            sites  = {'Common', 'Head', 'HeadNeck', 'ChestAbdo', 'Chest', 'Abdomen', 'Breast', 'PelvisF', 'PelvisM', 'Cardiac', 'TandO', 'Spine', 'TBI', 'BreastGP'};
            AddTasks_Segman(obj, 'CT','TPS', sites);
            
            sites  = {'Common', 'Head', 'ChestAbdo','Chest', 'Abdomen', 'PelvisM', 'PelvisF', 'BMs', 'GBM'};
            %sites  = {'Common', 'Head', 'Chest', 'Abdomen', 'PelvisM', 'PelvisF'};
            AddTasks_Segman(obj, 'MR','TPS', sites);
            %AddTasks_Segman(obj);
            obj.m_cfg = ExportBackEnd(obj);
            obj.writeJson; 
            frontend  = ExportFronEnd(obj);
        end
        
        
        
        function info = CreateTaskDef(posttaskfiles, varargin)
            info    = struct('IsAdminTask', 1); 
            options = OptionsMap(varargin{:});
            fns     = options.getOptionNames();
            for k=1:numel(fns)
                fn = fns{k};
                info.(fn) = options.getOption(fn); 
            end
            
            if ~isempty(posttaskfiles) && ischar(posttaskfiles)
                posttaskfiles = strsplit(posttaskfiles, '|'); 
            end
            PostTask=[];
            for k=1:numel(posttaskfiles)
                PostTask{k} = struct('taskfilename', posttaskfiles{k}); 
            end
            if ~isempty(PostTask)
                info.PostTask = PostTask;
            end
        end

        function defs = DcmServerPersistentTaskDef(segmanopt, outfile)
            % "taskname": "storeincoming",
            % "taskmode": "storefolder",
            % "storemode": 1,
            % "dcmfolder": "../DataBase/DicomData/incoming/",
            % "datetimerange": []
            m=0; 
            str = struct('taskname','storeincoming', 'taskmode', 'storefolder', 'storemode', 1, 'dcmfolder', './DataBase/DicomData/incoming/', 'datetimerange', []);
            m=m+1; defs{m} = str; 
            str = struct('taskname','storeincoming', 'taskmode', 'storefolder', 'storemode', 1, 'dcmfolder', '../Imports/Dcm/', 'datetimerange', [],'dcmfilepattern', "**/*" );
            m=m+1; defs{m} = str; 
            
            segsites    = segmanopt.getoptioni('sites'); 
            modalities  = segmanopt.getoptioni('modalities');
            DBNotes     = struct('Notes', '<SITE>');
            tasktemp    = struct('taskname','storeincoming', 'taskmode', 'storefolderwithsubtask', 'storemode', 1,...
                'dcmfolder', '../Imports/Segman/<SITE>/', 'datetimerange', [], 'dcmfilepattern', "**/*", 'DBNotes', DBNotes);
            subtasktemp = struct('Modality', '<MODALITY>', 'IsNewInstance', false,  'UIDType',  'SeriesInstanceUID', ...
                'OutputTaskFile', "../Tasks/MetsSeg/Segman_<MODALITY>_<SITE>/<MODALITY>.[SeriesInstanceUID].tsk");

            tasktemp = segmanopt.getoptioni('tasktemp', tasktemp);
            subtasktemp = segmanopt.getoptioni('subtasktemp', subtasktemp);

            for k=1:numel(segsites)
                site    = segsites{k};
                str     = tasktemp;
                %str.dcmfolder=strrep(str.dcmfolder, '<Site>', site);
                str = strrep_recursive(str, '<SITE>', site);
                DBNotes = struct('Notes', site);
                str.DBNotes = DBNotes;
                subtasks= [];
                for kk=1:numel(modalities)
                    modality= modalities{kk};
                    subtask = subtasktemp;
                    subtask = strrep_recursive(subtask, '<SITE>', site);
                    subtask = strrep_recursive(subtask, '<MODALITY>', modality);
                    subtasks{kk}=subtask;
                end
                str.subtasks=subtasks;
                m=m+1; defs{m} = str; 
            end
            if exist('outfile', 'var')
                utils.json.writeJson(defs, outfile);
            end
        end

        function cfg = CreateWebConfig(incfgfile, opts, outcfgfile)
            cfg = utils.json.readJson(incfgfile);
            DcmExportPort = opts.getoptioni('DcmExportPort');

            if ~isempty(DcmExportPort)
                if ischar(DcmExportPort)
                    DcmExportPort = strsplit(DcmExportPort, '|');
                end
                cfg.DcmExportPort=DcmExportPort;
            end

            ImportFolder = opts.getoptioni('ImportFolder');
            if ~isempty(ImportFolder)
                if ischar(ImportFolder)
                    ImportFolder= strsplit(ImportFolder, '|');
                end
                import_menu=[];
                for k=1:numel(ImportFolder)
                    name = ImportFolder{k};
                    task = struct('name', name, 'format','folder', 'server_folder', name);
                    import_menu{k} = task; 
                end
                cfg.import_menu=import_menu;
            end

            if exist('outcfgfile', 'var')
                utils.json.writeJson(cfg, outcfgfile);
            end
        end
    end
end

