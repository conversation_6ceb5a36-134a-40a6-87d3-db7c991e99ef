classdef nnUNET_PredDose < daemon.taskdef.nnUNETInference
    properties
        
    end

    methods
        function obj = nnUNET_PredDose(varargin)         
            <EMAIL>(varargin{:});
        end
    end

    methods (Static)
        function Factory_InitPlan(taskdeffolder)
            tasksdeffile = [taskdeffolder 'tasks_initplan.def'];
            taskdefname  = ['rtplan_FluenceMotion'];
            obj = utils.json.TaskDef([],{'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            TaskDoneIndicator=[taskdefname '.cfg'];
            defaultcfg = struct("DBTableName", "RTPlanDB",...
              "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
            obj.SetDef_struct(defaultcfg);
            rpfile ='../../../DataBase/DicomData/[PatientID]/[StudyInstanceUID]/RTPlan.[SeriesInstanceUID]/[SOPInstanceUID].dcm';
            process   = struct('DcmRTPlanFile', rpfile, ...
                'PlanMotionFile', 'PlanMotion.json', 'PlanFluenceFile', 'PlanFluence');
            obj.AddProcess('DcmRTPlan2FluenceMotion', process);
            %obj.AddDependency(dependency);
            obj.writeJson;
            obj.AddToTasksDefFile( tasksdeffile);

            taskdefname  = ['rtplan_FluenceMotionAper'];
            obj = utils.json.TaskDef([],{'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            TaskDoneIndicator=[taskdefname '.cfg'];
            defaultcfg = struct("DBTableName", "RTPlanDB",...
              "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
            obj.SetDef_struct(defaultcfg);
            process   = struct('DcmRTPlanFile', '../../DataBase/DicomData/[PatientID]/[StudyInstanceUID]/RTPlan.[SeriesInstanceUID]/[SOPInstanceUID].dcm', ...
                'PlanMotionFile', 'PlanMotion.json', 'PlanFluenceFile', 'PlanFluence', 'PlanAperFile', 'PlanAper');
            obj.AddProcess('DcmRTPlan2FluenceMotion', process);
            %obj.AddDependency(dependency);
            obj.writeJson;
            obj.AddToTasksDefFile( tasksdeffile);

            taskdefname  = ['rtplan_CT2Density'];
            ctfile ="../[ReferencedImageModality].[ReferencedImageSeriesUID]/image.nii.gz";
            synctfile ="../[ReferencedImageModality].[ReferencedImageSeriesUID]/SynCTm_Unity2mm/image.nii.gz";
            obj = utils.json.TaskDef([],{'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            TaskDoneIndicator=[taskdefname '.cfg'];
            defaultcfg = struct("DBTableName", "RTPlanDB",...
              "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
            obj.SetDef_struct(defaultcfg);
            process   = struct("CTFile", ctfile,...
        	    "DensityFile", "DensityImage");
            obj.AddProcess("CT2Density", process);
            obj.writeJson;
            obj.AddToTasksDefFile( tasksdeffile);

            taskdefname  = ['rtplan_MR2Density'];
            obj = utils.json.TaskDef([],{'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            TaskDoneIndicator=[taskdefname '.cfg'];
            defaultcfg = struct("DBTableName", "RTPlanDB",...
              "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
            obj.SetDef_struct(defaultcfg);
            Dependency=struct(...
	     	"filename",     synctfile,...
	     	"taskfilename", "../MetsSeg/SynCTm_Unity2mm/MR.[ReferencedImageSeriesUID].tsk");
            process   = struct("CTFile", synctfile,...
        	"DensityFile", "DensityImage");
            obj.AddProcess("CT2Density", process);
            obj.AddDependency(Dependency);
            obj.writeJson;
            obj.AddToTasksDefFile( tasksdeffile);

            taskdefname  = ['rtplan_Image2Density'];
            obj = utils.json.TaskDef([],{'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            TaskDoneIndicator=[taskdefname '.cfg'];
            defaultcfg = struct("DBTableName", "RTPlanDB",...
              "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
            obj.SetDef_struct(defaultcfg);
     
            Filter = struct("FieldName", "ReferencedImageModality",...
                 "MatchMode", "regexpi", "FieldValue",'MR', "Inclusion", true);
            Condition=struct("ConditionType", "InfoFilter",    "InfoSeq", 1,'Filter', Filter);
            process   = struct('Condition', Condition,...
                'TaskFileName', 'rtplan_MR2Density/RP.[SOPInstanceUID].tsk');
            obj.AddProcess("CreateTaskFile", process);
            process.Condition.Filter.FieldValue='CT';
            process.('TaskFileName')= 'rtplan_CT2Density/RP.[SOPInstanceUID].tsk';
            obj.AddProcess("CreateTaskFile", process);
            obj.writeJson;
            obj.AddToTasksDefFile( tasksdeffile);
        end

        function Factory_CalcDose(taskdeffolder)
           tasksdeffile = [taskdeffolder 'tasks_calcdose.def'];
           calcname = 'FCBB';
           taskdefname  = ['temp_CalculateDose' '_' calcname];
           obj = utils.json.TaskDef([],{'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
           TaskDoneIndicator=[taskdefname '.cfg']; 
           defaultcfg = struct("TaskOutputFolder", "[TaskOutputFolder]", ...
               "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
           Input = struct('SrcMotion', '[SrcMotionFile]', 'Fluence', '[FluenceFile]', 'Density', '[DensityFile]');
           Output= struct('DoseFile', '[DoseFile]');
           Dependency=struct(...
	     	"filename",     '[SrcMotionFile]|[FluenceFile]|[DensityFile]');
           AppOptions{1}=struct("OptionName", "exefile","OptionValue", "C:/ARTDaemon/distribute/wtkapp/bin/appDoseFactory.exe");
           AppOptions{2}=struct("OptionName", "in.RTBeamType","OptionValue", "[RTBeamType]");
           process   = struct("Input", Input,...
        	"Output", Output, 'DoseCalcName', calcname); 
           process.("AppOptions")=AppOptions;
            obj.SetDef_struct(defaultcfg);
            obj.AddProcess("CalculateDose", process);
            obj.AddDependency(Dependency);
            obj.writeJson;
            obj.AddToTasksDefFile( tasksdeffile);

           taskdefname  = ['rtplan_CalculateDose' '_' calcname];
           TaskDoneIndicator=[taskdefname '.cfg']; 
           obj = utils.json.TaskDef([],{'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
            defaultcfg = struct("DBTableName", "RTPLANDB", "CustomSubFolder", calcname, ...
               "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
            obj.SetDef_struct(defaultcfg);
            Dependency=struct("filename",     "../DensityImage.nii.gz",...
                "taskfilename", "rtplan_Image2Density/RP.[SOPInstanceUID].tsk");
            obj.AddDependency(Dependency);

            Dependency=struct("filename",     "../PlanFluence.nii.gz",...
                "taskfilename", "rtplan_FluenceMotionAper/RP.[SOPInstanceUID].tsk");
            obj.AddDependency(Dependency);
            
            TaskInfo=struct("FileName", ['temp_CalculateDose' '_' calcname '/RP.[SOPInstanceUID].tsk']);
            TaskInfo.Info=struct(...
                    "RTBeamType", "unity",...
					"DoseCalcName", calcname,...
					"TaskOutputFolder", ['[PatientID]/RP.[SOPInstanceUID]/' calcname '/'],...
					"SrcMotionFile", "../PlanMotion.json", ...
					"FluenceFile", "../PlanFluence.nii.gz", ...
                    "DensityFile", "../DensityImage.nii.gz");
            Dependency=struct("filename",     "PlanDose.nii.gz",...
                "TaskInfo", TaskInfo);
            obj.AddDependency(Dependency);
            obj.writeJson;
            obj.AddToTasksDefFile( tasksdeffile); 
        end

        function Factory_PredDose_unity(taskdeffolder)
            nnUNETModelRoot = 'C:\ARTDaemon\distribute\nnUNETDatav2.unity20241130\';
            datesetnums={'513', '523', '533'};
            modalities ={'normbeam', 'initbeam', 'initbeam5mm'};
            TrainerName ='#nnUNetTrainer__nnUNetPlans__3d_fullres#fold_0';
            modelids = cellfun(@(num, modality)(['Dataset' num '_unity20241130_prescription-avoidance-' modality '2doseNormRx' TrainerName]), ...
                 datesetnums, modalities, 'UniformOutput',false);
            
            tasksdeffile = [taskdeffolder 'tasks_PredDose_unity.def'];
            %subfoldername = 'PredDose_unity';
            InputImage = "[PrescriptionImage]|[AvoidanceImage]|[BeamImage]";
            for k=1:numel(modelids)
                modelid = modelids{k};
                ModelOptionFile=DosUtil.SimplifyPath([nnUNETModelRoot modelid '.opt']);
                modality = modalities{k};
                OutputFile ='PredDose';
                %dependtask ="../DcmConverter/DCMCONVERT_RP/RP.[SOPInstanceUID].tsk";
                dependency = struct("filename", InputImage);
                taskdefname = ['temp_nnUNET_PredDose_unity_' modality];
                obj = daemon.taskdef.nnUNETInference({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname}, ...
                    {'ModelOptionFile', ModelOptionFile}, ...
                    {'InputImage',  InputImage}, ...
                    {'OutputFile', OutputFile});
                TaskDoneIndicator=['Preddose-' modality '.cfg'];
                defaultcfg = struct(...
                  "TaskOutputFolder",'[TaskOutputFolder]', ...
                  "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
                obj.SetDef_struct(defaultcfg);
                obj.AddDependency(dependency);
                obj.writeJson;
                obj.AddToTasksDefFile( tasksdeffile);
            end
        end
    end
end