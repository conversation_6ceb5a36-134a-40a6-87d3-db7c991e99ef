classdef nnUNET_BraTS2021_GBMPreOp < daemon.taskdef.nnUNETInference
    properties
        
    end

    methods
        function obj = nnUNET_BraTS2021_GBMPreOp(varargin)         
            <EMAIL>(varargin{:});
        end
    end

    methods (Static)
        function Factory_ch1(tasksdeffile)
            taskdeffolder = [fileparts(tasksdeffile) '/'];
            datesetnums ={'103', '113', '123', '133'};
            modalities = {'T1nc', 'T1c', 'T2', 'Flair'};
            modalities0 =cellfun(@(x)(strrep(x, 'T1nc','T1')), modalities, 'UniformOutput',false );
            modalities0 =cellfun(@(x)(strrep(x, 'T1c','T1ce')), modalities0, 'UniformOutput',false );
            TrainerName ='#nnUNetTrainer__nnUNetPlans__3d_fullres_T#fold_0';
            modelids = cellfun(@(num, modality)(['Dataset' num '_BraTS2021_' modality '2TC-ET-ED' TrainerName]), ...
                 datesetnums, modalities0, 'UniformOutput',false);
            nnUNETModelRoot = 'C:\ARTDaemon\distribute\nnUNETDatav2.BraTS2021_GBMPreOp\';
            %tasksdeffile = [taskdeffolder 'tasks_GBMPreOp.def'];
            %subfoldername = 'nnUNET_BraTS2021_GBMPreOp';
            subfoldername = 'BraTS2021_GBMPreOp';
            for k=1:numel(modelids)
                modelid = modelids{k};
                ModelOptionFile=DosUtil.SimplifyPath([nnUNETModelRoot modelid '.opt']);
                modality = modalities{k};
                InputImage = "../nnUNET_Brain/image_brain.nii.gz";
                % OutputFile ='labelmask';
                % dependtask ="SEG_AIAtlas_MR_MNI_nnUNET_Brain/MR.[SeriesInstanceUID].tsk";
                % dependency = struct("filename", InputImage,...
                %     "taskfilename", dependtask);
                % taskdefname = ['MR_' subfoldername '_' modality];
                % obj = daemon.taskdef.nnUNET_BraTS2021_GBMPreOp({'TaskDefFolder', taskdeffolder}, ...
                %     {'TaskDefName', taskdefname}, ...
                %     {'ModelOptionFile', ModelOptionFile}, ...
                %     {'InputImage',  InputImage}, ...
                %     {'OutputFile', OutputFile});
                % TaskDoneIndicator=[modality '.cfg'];
                % defaultcfg = struct("DBTableName", "MR",...
                %   "CustomSubFolder", ['MNI/' subfoldername], ...
                %   "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
                % obj.SetDef_struct(defaultcfg);
                % obj.AddDependency(dependency);
                % obj.writeJson;
                % obj.AddToTasksDefFile( tasksdeffile);
                dependtask ="SEG_AIAtlas_MR_MNI_nnUNET_Brain/MR.[SeriesInstanceUID].tsk";
                daemon.taskdef.nnUNET.InferenceImage(ModelOptionFile, tasksdeffile,{'InputModality', 'MR'},...
                   {'ModelName', [subfoldername '_' modality]},  {'InputImage', InputImage}, {'CustomSubFolder',['MNI/' subfoldername]}, {'DependentTaskFile', dependtask});
            end
     
            taskdefname = ['MR_' subfoldername];
            % obj = utils.json.TaskDef([],{'TaskDefFolder', taskdeffolder}, ...
            %         {'TaskDefName', taskdefname});
            % TaskDoneIndicator=['task.cfg'];
            % defaultcfg = struct("DBTableName", "MR",...
            %       "CustomSubFolder", [taskdefname], ...
            %       "IsCustomTask", 1,...
            %       "TaskDoneIndicator", TaskDoneIndicator, "TaskConfigOutput", TaskDoneIndicator);
            %  obj.SetDef_struct(defaultcfg);
            obj = daemon.taskdef.nnUNET({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
             %modalities1 =  modalities;  modalities1{1}='T1_noce';
             seriesfilters =    daemon.taskdef.TaskDef.MRDescriptionFilters(modalities);
             Process=[];
             for k=1:numel(modelids)
                 modality = modalities{k};
                 %Filter = utils.json.TaskDef.MR_SeriesFilter(modality);
                 Filter = seriesfilters{k};
                 Condition=struct("ConditionType", "InfoFilter",...
                     "InfoSeq", 1,...
                     "Filter", Filter); 
                 taskfilename = [taskdefname '_' modality '/MR.[SeriesInstanceUID].tsk'];
                 Process{k} = struct('Condition', Condition, "taskfilename", taskfilename);
                 %obj.AddProcess([],process);
             end
             obj.Generate(tasksdeffile, [], Process, {'CustomSubFolder', ['MNI/'   subfoldername]});
             % obj.writeJson;
             % obj.AddToTasksDefFile( tasksdeffile);

            segmanID = ['GBMPreop'];
            SrcRoiMaskFile=['../MNI/' subfoldername '/labelmask.nii.gz'];
            MergeOperation=struct("SrcRoiMaskFile", SrcRoiMaskFile);
            taskdefname0 = ['MR_' subfoldername];
            PostProcessing=PostProcessing_GBMPreop();
            dependency=struct("filename", SrcRoiMaskFile, "taskfilename", [taskdefname0 '/MR.[SeriesInstanceUID].tsk']);
            daemon.taskdef.TaskDef.CreateSegmanDef(tasksdeffile,['Segman_MR_' segmanID],'MR', segmanID, dependency,MergeOperation, PostProcessing);
        end

        function Factory_study(tasksdeffile)
            TrainerName ='#nnUNetTrainer__nnUNetPlans__3d_fullres_T#fold_0';
            nnUNETModelRoot = 'C:\ARTDaemon\distribute\nnUNETDatav2.BraTS2021_GBMPreOp\';
            %tasksdeffile = [taskdeffolder 'tasks_GBMPreOp.def'];
            subfoldername = 'BraTS2021_GBMPreOp';

            datesetnums={'203', '213', '303', '403'};
            modalities ={'T1c-Flair', 'T1nc-T1c', 'T1nc-T1c-Flair', 'T1nc-T1c-T2-Flair'};
            modalities0 =cellfun(@(x)(strrep(x, 'T1nc','T1')), modalities, 'UniformOutput',false );
            modalities0 =cellfun(@(x)(strrep(x, 'T1c','T1ce')), modalities0, 'UniformOutput',false );
            modelids = cellfun(@(num, modality)(['Dataset' num '_BraTS2021_' modality '2TC-ET-ED' TrainerName]), ...
            datesetnums, modalities0, 'UniformOutput',false);
      
            PostProcessing=PostProcessing_GBMPreop();
            for k=1:numel(modelids)
                modelid = modelids{k};
                ModelOptionFile=DosUtil.SimplifyPath([nnUNETModelRoot modelid '.opt']);
                modality = modalities{k};
                modality0 = strrep(modality, '-', '');
                modality1 = strsplit(modality, '-');
                InputImage=cellfun(@(x)(['MR_' x '/MNI/nnUNET_Brain/image_brain']), modality1, 'uniformoutput', false);
                InputImage=xls.TableBase.Content2Str(InputImage);
                modelname =[subfoldername '_' modality0];
                daemon.taskdef.nnUNET.InferenceStudy(ModelOptionFile, tasksdeffile, {'ModelName', modelname}, {'InputChannels', InputImage}, {'InputModality', 'MR'});
                
                segmanID = ['GBMPreOp_' modality0];
                SrcRoiMaskFile=['../../STUDY.[StudyInstanceUID]/' modelname '/labelmask.nii.gz'];
                MergeOperation=struct("SrcRoiMaskFile", SrcRoiMaskFile);
                taskdefname0 = ['MR_' modelname];
                dependency=struct("filename", SrcRoiMaskFile, "taskfilename", [taskdefname0 '/MR.[SeriesInstanceUID].tsk']);
                daemon.taskdef.TaskDef.CreateSegmanDef(tasksdeffile, ['Segman' segmanID],'MR', segmanID, dependency,MergeOperation, PostProcessing);
            end
        end

         function Factory_ExtraSource(tasksdeffile)
            TrainerName ='#nnUNetTrainer__nnUNetPlans__3d_fullres_T#fold_0';
            nnUNETModelRoot = 'C:\ARTDaemon\distribute\nnUNETDatav2.BraTS2021_GBMPreOp\';
            %tasksdeffile = [taskdeffolder 'tasks_GBMPreOp.def'];
            subfoldername = 'BraTS2021_GBMPreOp';

            datesetnums={'203', '213', '303', '403'};
            modalities ={'T1c-Flair', 'T1nc-T1c', 'T1nc-T1c-Flair', 'T1nc-T1c-T2-Flair'};
            modalities0 =cellfun(@(x)(strrep(x, 'T1nc','T1')), modalities, 'UniformOutput',false );
            modalities0 =cellfun(@(x)(strrep(x, 'T1c','T1ce')), modalities0, 'UniformOutput',false );
            modelids = cellfun(@(num, modality)(['Dataset' num '_BraTS2021_' modality '2TC-ET-ED' TrainerName]), ...
            datesetnums, modalities0, 'UniformOutput',false);
      
            PostProcessing=PostProcessing_GBMPreop();
            for k=1:numel(modelids)
            %for k=1
                modelid = modelids{k};
                ModelOptionFile=DosUtil.SimplifyPath([nnUNETModelRoot modelid '.opt']);
                modality = modalities{k};
                modality0 = strrep(modality, '-', '');
                modality1 = strsplit(modality, '-');
                % InputImage=cellfun(@(x)(['MR_' x '/MNI/nnUNET_Brain/image_brain']), modality1, 'uniformoutput', false);
                % InputImage=xls.TableBase.Content2Str(InputImage);
                modelname =[subfoldername '_' modality0];
                % daemon.taskdef.nnUNET.InferenceStudy(ModelOptionFile, tasksdeffile, {'ModelName', modelname}, {'InputChannels', InputImage}, {'InputModality', 'MR'});
                % 
                taskdefname=['temp_' subfoldername '_' modality0];
                daemon.taskdef.nnUNET.InferenceDef(tasksdeffile, taskdefname, ModelOptionFile);
                taskdefname=['MR_' subfoldername '_' modality0];
                daemon.taskdef.nnUNET_BraTS2021_GBMPreOp.InferenceDef_ExtraSource(tasksdeffile, taskdefname, ModelOptionFile,  modality1);

                segmanID = ['GBMPreOp_' modality0];
                %SrcRoiMaskFile=['../../STUDY.[StudyInstanceUID]/' modelname '/labelmask.nii.gz'];
                SrcRoiMaskFile=['../MNI/' modelname '/labelmask.nii.gz'];
                MergeOperation=struct("SrcRoiMaskFile", SrcRoiMaskFile);
                taskdefname0 = ['MR_' modelname];
                dependency=struct("filename", SrcRoiMaskFile, "taskfilename", [taskdefname0 '/MR.[SeriesInstanceUID].tsk']);
                daemon.taskdef.TaskDef.CreateSegmanDef(tasksdeffile, ['Segman' segmanID],'MR', segmanID, dependency,MergeOperation, PostProcessing);
            end
         end

         function InferenceDef_ExtraSource(tasksdeffile, taskdefname, modeloptfile,  modalities, varargin)
            opts = OptionsMap(varargin{:});
            % prefix = extractBefore(taskdefname, '_');
            % m=0;   modality=[]; InputImage=[]; ModalityUID=[]; dependency=[];
            % m=m+1; modality{m} = prefix; ModalityUID{m}   =[modality{m} '.[SeriesInstanceUID]'];
            % extrasource=StructBase.toCell(extrasource);
            extrasource=[];
            for k=2:numel(modalities)
                modality = modalities{k};
                [filters] = daemon.taskdef.qMRI.MRSeqFilter({modality}, 10);
                filter_FOR= struct("FieldName", 'FrameOfReferenceUID',	'FieldValue', '[FrameOfReferenceUID]', "MatchMode", 'strcmp');
                filters = cat(2, filters, {filter_FOR});
                extrasource{k-1} = struct('DBTableName', 'MR'); 
                extrasource{k-1}.Filter = filters; 
            end
            m=1; ModalityUID{m} = ['MR.[SeriesInstanceUID]'];
            for k=1:numel(extrasource)
                m=m+1; 
                %modality{m}=StructBase.getfieldx(extrasource{k}, 'DBTableName');
                if m==2
                    ModalityUID{m} = ['MR.{SeriesInstanceUID}'];
                elseif m==3
                    ModalityUID{m} = ['MR.(SeriesInstanceUID)'];
                elseif m==4
                    ModalityUID{m} = ['MR.<SeriesInstanceUID>'];
                end
            end
            modelname = extractAfter(taskdefname, 'MR_');
            CustomSubFolder=['MNI/' modelname];
            InputImage=cellfun(@(x)(['../../../' x '/MNI/nnUNET_Brain/image_brain.nii.gz']), ModalityUID, 'uniformoutput', false);
            
            dependency= cellfun(@(inputimage, modalityuid)(struct('filename',inputimage,...
                 'taskfilename', ['SEG_AIAtlas_MR_MNI_nnUNET_Brain/' modalityuid '.tsk'])), InputImage, ModalityUID, 'UniformOutput',false); 
            InputImage=xls.TableBase.Content2Str(InputImage);
            ExtraDef = struct('ExtraSource', extrasource, 'CustomSubFolder', CustomSubFolder);
            InferenceCfg = struct("ReformCS2Channel", 1);
            InputImage=opts.getOption('InputImage', InputImage);
            dependency=opts.getOption('Dependency', dependency);
            daemon.taskdef.nnUNET.InferenceDef(tasksdeffile, taskdefname, modeloptfile, {'InputImage', xls.TableBase.Content2Str(InputImage)},...
                {'Dependency', dependency}, {'InferenceCfg', InferenceCfg}, {'ExtraDef', ExtraDef });
        end
    end
end

function PostProcessing=PostProcessing_GBMPreop
    m=0;   PostProcessing=[];
    m=m+1; PostProcessing{m} = struct(...
      "OperationType",'ROIBoolean',...
      "OperationName",'ROIUnion',...
      "SourceROINames",'ET|NCR',...
      "DestinationROIName",'TC');

    m=m+1; PostProcessing{m} = struct(...
      "OperationType",'ROIBoolean',...
      "OperationName",'ROIUnion',...
      "SourceROINames",'ET|NCR|ED',...
      "DestinationROIName",'WT');

    m=m+1; PostProcessing{m} = struct(...
      "OperationType",'RemoveROIs',...
      "ROIName",'none|NCR|ED');
end