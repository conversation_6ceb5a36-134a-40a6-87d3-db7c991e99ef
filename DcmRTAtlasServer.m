classdef DcmRTAtlasServer < daemon.DcmRTServer
    properties
        
    end
    
    methods
        function obj = DcmRTAtlasServer(varargin)
            options = OptionsMap();
            options.setOption('ServerName','Atlas'); 
            <EMAIL>(options, varargin{:});
        end
        
        function status = CalcAdminTask(self, task, info, varargin)
            status         = <EMAIL>(task, info, varargin{:});
            taskdeffolder  = self.TasksDefFolder;
            datarootfolder = self.OutputRootFolder;
            dbstr = task.GetTaskConfig('DataBase');
            rsT = [];
            if ~isempty(dbstr)
                rsT = LoadDatabase(self, dbstr);
            end

            AltasStr=task.GetTaskConfig('CreateAtlas');
            if ~isempty(AltasStr)
                N = size(rsT, 1);
                for n=1:N
                    try
                    info = table2struct(rsT(n, :));
                    self.CreateAtlas(AltasStr, info, datarootfolder, {'TaskDefFolder', taskdeffolder});
                    catch err
                        self.LogWarning(info.CaseID);
                    end
                end
            end
            
            AtlasStr=task.GetTaskConfig('CropAtlas');
            if ~isempty(AtlasStr)
                % N = size(rsT, 1);
                % for n=1:N
                %     try
                %     info = table2struct(rsT(n, :));
                %     self.ShowAtlas(AltasStr, info, datarootfolder, {'TaskDefFolder', taskdeffolder});
                %     catch err
                %         self.LogWarning(info.CaseID);
                %     end
                % end
                caseT    = xls.TableBase.Convert2NumericVariables(rsT, 'RefPoint');
                imgfiles = caseT.ImageFile; 
                roimaskfiles = caseT.ROIMaskFile;
                RefPoint = caseT.RefPoint; 
                cropfile = StructBase.getfieldx(AtlasStr, 'CropTableXlsFile');
                cropfile =  daemon.DcmRTServer.toFullFile(cropfile, [],datarootfolder);
                cropT = xls.TableBase.ReadTable(cropfile);
                numvars = setdiff(cropT.Properties.VariableNames, 'ROIName'); 
                cropT     = xls.TableBase.Convert2NumericVariables(cropT, numvars);
                %roinames  = 'LN_Ax_L1_L';
                roinames = StructBase.getfieldx(AtlasStr, 'ROIs');
                IgnoreCS = StructBase.getfieldx_default(AtlasStr, 'IgnoreCS', 0);
                ShowAtlas = StructBase.getfieldx_default(AtlasStr, 'ShowAtlas', 0);
                outroot = StructBase.getfieldx(AtlasStr, 'OutputRoot');
                outroot = daemon.DcmRTServer.toFullFile( outroot, [],datarootfolder);
                %roinames = 'LAD';
                roinames = strsplit(roinames, '|');
                ai.atlas.AtlasStat.CreateCropAtlases(imgfiles, roimaskfiles, RefPoint,cropT, roinames, outroot, ...
                   {'ShowAtlas', ShowAtlas}, {'IgnoreCS', IgnoreCS});
            end

            AltasStr=task.GetTaskConfig('ShowAtlas');
            if ~isempty(AltasStr)
                AltasStr = StructBase.toCell(AltasStr);
                for m=1:numel(AltasStr)
                    N = size(rsT, 1);
                    for n=1:N
                        try
                        info = table2struct(rsT(n, :));
                        self.ShowAtlas(AltasStr{m}, info, datarootfolder, {'TaskDefFolder', taskdeffolder});
                        catch err
                            self.LogWarning(info.CaseID);
                        end
                    end
                end
            end
            
            AtlasStr=task.GetTaskConfig('AtlasStat');
            if ~isempty(AtlasStr)
                CollectAtlasStat(self, AtlasStr, rsT);
            end

            trainStr=task.GetTaskConfig('Train_nnUNET');
            if ~isempty(trainStr)
                templateguide =  StructBase.getfieldx_default(trainStr,   'TemplateGuidance', 0);
                if templateguide
                    [dataset]   = self.PrepareTrain_nnUNET_templateguide(trainStr, rsT, datarootfolder);
                else
                    [dataset]   = self.PrepareTrain_nnUNET(trainStr, rsT, datarootfolder);
                end
            end
            
            AtlasTask = task.GetTaskConfig('AtlasTask');
            if ~isempty(AtlasTask)
                 templatefile =StructBase.getfieldx(AtlasTask, 'TemplateTaskFile');
                 if ~isempty(templatefile)
                     N = size(rsT, 1);
                     for n= 1:N
                        info = table2struct(rsT(n, :));
                        taskfiles{n, 1} = daemon.DcmRTServer.toFullFile(templatefile, info,datarootfolder);
                     end
                    %cellfun(@(x)(fclose(fopen(x, 'wt'))), taskfiles);
                    cellfun(@(x)(DosUtil.CreateEmptyFile(x)), taskfiles);
                 end
            end
            
            AtlasTask = task.GetTaskConfig('AtlasClassify');
            if ~isempty(AtlasTask)
                self.DecomposeTables(rsT, AtlasTask, datarootfolder);
            end
        end

        function rsT = LoadDatabase(self,dbstr)
            excelstr = StructBase.getfieldx(dbstr, "Excel");
            if ~isempty(excelstr)
                datarootfolder = self.OutputRootFolder;
                xlsfname  = StructBase.getfieldx(excelstr, 'FileName');
                xlsfname  = self.toFullFile(xlsfname, [], datarootfolder);
                sheetname = StructBase.getfieldx(excelstr,'SheetName');
                rsT       = xls.TableBase.ReadTable(xlsfname, {'table.sheetname', sheetname});
            end
            
            plandbstr = StructBase.getfieldx(dbstr, "RTPlanDB");
            if ~isempty(plandbstr)
                querystr= StructBase.getfieldx_default(plandbstr, 'QueryStr', ''); 
                dbname  = StructBase.getfieldx_default(plandbstr, 'DBTableName', ''); 
                rsT = self.m_DB.QueryDBTable(dbname, querystr);
            end
            numvars = StructBase.getfieldx(dbstr, 'NumericVariables');
            if ~isempty(numvars)
                rsT = xls.TableBase.Convert2NumericVariables(rsT, numvars);
            end
            filter = StructBase.getfieldx(dbstr,'TableFilter'); 
            if ~isempty(filter)
                dbfilter = xls.TableFilter; 
                dbfilter.AddTableFilter(filter);
                [~, rsT] = dbfilter.FilterTable(rsT); 
            end
        end

        function CollectAtlasStat(self, AltasStr, rsT)
            datarootfolder = self.OutputRootFolder;       
            outxlsfile  =StructBase.getfieldx(AltasStr, 'OutputXlsFile');
            %roiinfonames=StructBase.getfieldx_default(AltasStr, 'ROIInfoNames', 'ROICenter|ROIVolume|BoundingBox');
            roiinfonames=StructBase.getfieldx_default(AltasStr, 'ROIInfoNames', 'ROICenter|ROIVolume|BBSize|BBCenter|VolHeader');
            roinames  = StructBase.getfieldx(AltasStr, "ROINames");
            nomenstr  = StructBase.getfieldx(AltasStr, 'Nomenclature'); 
            taskdeffolder = self.TasksDefFolder;
            nomen=self.ParseNomenclature(nomenstr, taskdeffolder);
            if isempty(roinames)&& ~isempty(nomen)
                roinames = nomen.ListStandardID();
            end
            otheropts = StructBase.getfieldx(AltasStr, 'OtherOptions');
            otheropts = OptionsMap.struct2options(otheropts);
            if ~exist(outxlsfile, 'file')
                roimaskfname = StructBase.getfieldx(AltasStr, 'ROIMaskFile');
                N    = size(rsT);
                for n= 1:N
                    info = table2struct(rsT(n, :));
                    if ischar(roimaskfname)
                        roimaksfiles{n, 1} = daemon.DcmRTServer.toFullFile(roimaskfname, info,datarootfolder);
                    elseif iscell(roimaskfname)
                        fnames=[];
                        for k=1:numel(roimaskfname)
                            fnames{k} = daemon.DcmRTServer.toFullFile(roimaskfname{k}, info,datarootfolder);
                        end
                        roimaksfiles{n, 1}=fnames;
                    end
                end

                self.CollectAtlasInfo(roinames,  roimaksfiles, outxlsfile, roiinfonames,...
                    {'roimap.Nomenclature', nomen}, otheropts);
            end
           
            RefCenter0=StructBase.getfieldx(AltasStr, 'RefCenter');  RefCenter=[];
            RefStr = StructBase.getfieldx_default(AltasStr, 'StatAnnotation', '');
            if isstruct(RefCenter0)
                RefROIName=StructBase.getfieldx_default(RefCenter0, 'RefROIName', '');
                if isempty(RefStr)
                    RefStr = ['_Ref' RefROIName];
                end
                for n=1:N
                    try
                    if isstruct(RefCenter0)
                        RefCenter(n, :) = self.ParseRefCenter(RefCenter0, info,datarootfolder);
                    end
                    catch
                        RefCenter(n, :) = NaN(1, 3);
                    end
                end
            else
                 RefCenter = RefCenter0;
                 if ischar(RefCenter) &&isempty(RefStr)
                     RefStr = ['_Ref' RefCenter];
                 end
            end
            
            summary = StructBase.getfieldx(AltasStr, 'StatSummary'); 
%             refcenter = options.getoptioni('RefCenter'); 

            ai.atlas.AtlasStat.CollectAtlasStat(outxlsfile, RefCenter, {'ROIStatNames', roiinfonames}, ...
                {'StatSummary', summary}, {'RefStr', RefStr}, otheropts);
        end
    end

    methods (Static)
        function [dataset]   = PrepareTrain_nnUNET(trainStr, rsT, datarootfolder)
                
                rsT    = xls.TableBase.Convert2NumericVariables(rsT, {'IsTest', 'IsTrain', 'IsOutlier', 'IsTemplate'});
                IDName = StructBase.getfieldx_default(trainStr, 'IDName', 'CaseID');
                if ~ismember(IDName, rsT.Properties.VariableNames)
                    N = size(rsT, 1);
                    caseids =arrayfun(@(x)(num2str(x)), [1:N]', 'UniformOutput',false);
                    rsT.(IDName) = caseids; 
                end
                N = size(rsT, 1);
                varnames = rsT.Properties.VariableNames; 
                if ismember('IsTest', varnames)
                    testI = rsT.('IsTest'); 
                else
                    testI = zeros(N, 1, 'like', false);
                end
                if ismember('IsOutlier', varnames)
                    IsOutlier = rsT.('IsOutlier'); 
                else
                    IsOutlier = zeros(N, 1, 'like', false);
                end
                testI = testI &~IsOutlier; 
                OutlierCases=StructBase.getfieldx_default(trainStr, 'OutlierCases', []);
                if ~isempty(OutlierCases)
                    %rsT(OutlierCases, :)=[];
                    testI( OutlierCases)=1;
                end

                TestCases=StructBase.getfieldx_default(trainStr, 'TestCases', []);
                if ~isempty(TestCases)
                    %rsT(OutlierCases, :)=[];
                    testI( TestCases)=1;
                end
                
                TestMod = StructBase.getfieldx_default(trainStr, 'TestMod', 0);
                if TestMod>0
                    I = arrayfun(@(x)(mod(x, TestMod)==0), [1:N]'); 
                    %rsT(I, :)=[];
                    testI=testI|I;
                end

                nnunetroot = StructBase.getfieldx(trainStr, 'nnUNETRoot');  
                taskname   = StructBase.getfieldx(trainStr,   'TaskName');  
                labels     = StructBase.getfieldx(trainStr,   'LabelName');   
                InputChannel=StructBase.getfieldx(trainStr, 'InputChannel');   
                modality = StructBase.getfieldx(trainStr,   'InputModality');
                

                if ischar(InputChannel)
                    InputChannel = strsplit(InputChannel, '|'); 
                end
                imgfiles=[];
                for k=1:numel(InputChannel)
                    chfiles= daemon.DcmRTAtlasServer.Table2FullFiles(rsT, InputChannel{k}, datarootfolder); 
                    imgfiles= cat(2, imgfiles, chfiles);
                end

                labelfiles=[];
                LabelFile=StructBase.getfieldx(trainStr,    'LabelFile');
                if ~isempty(LabelFile)
                    if ischar(LabelFile)
                        labelfiles = daemon.DcmRTAtlasServer.Table2FullFiles(rsT, LabelFile, datarootfolder); 
                    elseif iscell(LabelFile)
                        labelfiles=[];
                        for k=1:numel(LabelFile)
                            chfiles= daemon.DcmRTAtlasServer.Table2FullFiles(rsT, LabelFile{k}, datarootfolder); 
                            labelfiles= cat(2, labelfiles, chfiles);
                        end
                    end
                end

                outimgfiles=[];
                OutImageFile=StructBase.getfieldx(trainStr,    'OutImageFile');
                if ~isempty(OutImageFile)
                    if ischar(OutImageFile)
                        outimgfiles = daemon.DcmRTAtlasServer.Table2FullFiles(rsT, OutImageFile, datarootfolder); 
                    elseif iscell(OutImageFile)
                        outimgfiles=[];
                        for k=1:numel(OutImageFile)
                            chfiles= daemon.DcmRTAtlasServer.Table2FullFiles(rsT, OutImageFile{k}, datarootfolder); 
                            outimgfiles= cat(2, labelfiles, chfiles);
                        end
                    end
                end


%                 N = size(labelfiles, 1);
%                 M = size(labelfiles, 2);
                
                checkfile = StructBase.getfieldx_default(trainStr, 'CheckFileExist', 1);   
                nonExistI = zeros(N, 1, 'like', false);
                if checkfile
                    
                    %N = size(labelfiles, 1);
                    I = zeros(N, 1, 'like', false);
                    I1= ones(N, 1, 'like', false);
                    if ~isempty(labelfiles)
                        for k=1:size(labelfiles, 2)
                            I1 = I1 & cellfun(@(x)(~exist(x, 'file')), labelfiles(:, k));
                        end
                    end
                    
                    if ~isempty(outimgfiles)
                        for k=1:size(outimgfiles, 2)
                            I1 = I1 & cellfun(@(x)(~exist(x, 'file')), outimgfiles(:, k));
                        end
                    end
    
                   
                    I  = I|I1;
                    for k=1:numel(InputChannel)
                        I1 =cellfun(@(x)(~exist(x, 'file')), imgfiles(:, k));
                        I  = I|I1;
                    end
                    %testI=testI|I;
                    nonExistI = I; 
                end  

                RequiredFile=StructBase.getfieldx(trainStr,    'RequiredFile');
                if ~isempty(RequiredFile)
                     if ischar(RequiredFile)
                        RequiredFile=strsplit(RequiredFile, '|');
                     end
                     for m=1:numel(RequiredFile)
                         requiredFiles = daemon.DcmRTAtlasServer.Table2FullFiles(rsT, RequiredFile{m}, datarootfolder); 
                         I1 =cellfun(@(x)(~exist(x, 'file')), requiredFiles);
                         nonExistI = nonExistI|I1; 
                     end
                end

                caseids      =  rsT.CaseID;
                testI        = testI & ~nonExistI; 
                if ismember('IsTrain',rsT.Properties.VariableNames)
                    trainI       = rsT.IsTrain; 
                else
                    trainI       = ~testI & ~nonExistI&~IsOutlier; 
                end
%                 TrainCases   = find(trainI);
%                 trainimgfiles= imgfiles(TrainCases,:); 
%                 trainlabelfiles=labelfiles(TrainCases, :);
%                 traincaseids = caseids(TrainCases);
% %                 N = numel(TrainCases);
%                 M = size(labelfiles, 2);
                % templateguide =  StructBase.getfieldx_default(trainStr,   'TemplateGuidance', 0);
                %NumberOfTemplates = StructBase.getfieldx_default(templateguide, 'NumberOfTemplates', 0);
                extraopt = StructBase.getfieldx(trainStr,'OtherOption');
                if isstruct(extraopt)
                    extraopt=OptionsMap.struct2options(extraopt); 
                end
  
                nnUNETVersion =  StructBase.getfieldx_default(trainStr,   'nnUNETVersion', 'v1');
                CONFIGURATION =  StructBase.getfieldx_default(trainStr,   'CONFIGURATION', '3d_fullres');
                trainclasses=StructBase.getfieldx_default(trainStr, 'TrainClass');
                if isempty(trainclasses)
                    if strcmpi(nnUNETVersion, 'v1')
                        trainclasses= 'nnUNetTrainerV2_ep4000_nomirror|nnUNetTrainerV2_ep4000';
                    elseif strcmpi(nnUNETVersion, 'v2')
                        trainclasses= 'nnUNetTrainerNoMirroring|nnUNetTrainer';
                    end
                end
                trainopts = OptionsMap({'nnUNETVersion', nnUNETVersion}, {'CONFIGURATION', CONFIGURATION},{'trainclasses',trainclasses});
                LabelImageType =  StructBase.getfieldx_default(trainStr,   'LabelImageType', 'labelmask');
                LabelImageIntensityRef =  StructBase.getfieldx_default(trainStr,   'LabelImageIntensityRef', []);
                trainopts.setOptions({'LabelImageType', LabelImageType}, {'LabelImageIntensityRef', LabelImageIntensityRef});
                
                extraTrainOpt = StructBase.getfieldx_default(trainStr, 'extraTrainOpt');
                if ~isempty(extraTrainOpt)
                    trainopts.setOption('extraTrainOpt', extraTrainOpt);
                end
                
                if strcmpi(nnUNETVersion, 'v1')
                    dataset = nnunet.DatasetJson.PrepareTrain(taskname,  modality, labels, imgfiles, labelfiles,...
                      {'nnunet.root',nnunetroot}, {'ImageID', caseids}, {'WithTestData', 0}, {'IsTrain', trainI}, {'IsTest', testI}, trainopts, extraopt);
                elseif strcmpi(nnUNETVersion, 'v2')
                     dataset = nnunet.DatasetJsonV2.PrepareTrain(taskname,  modality, labels, imgfiles, labelfiles,{'OutImages', outimgfiles},...
                      {'nnunet.root',nnunetroot}, {'ImageID', caseids}, {'WithTestData', 0}, {'IsTrain', trainI}, {'IsTest', testI}, trainopts, extraopt);
                end
                   
                %optfile = ai.seg.ImageSeg_nnUNET.WriteModelOptFile(nnunetroot, taskname, labels, trainopts);
        end

        function [dataset]   = PrepareTrain_nnUNET_templateguide(trainStr, rsT, datarootfolder)
                rsT    = xls.TableBase.Convert2NumericVariables(rsT, {'IsTest', 'IsTrain', 'IsOutlier', 'IsTemplate', 'ROIIndex'});
                N = size(rsT, 1);
                varnames = rsT.Properties.VariableNames; 
                IDName = StructBase.getfieldx_default(trainStr, 'IDName', 'CaseID');
                
                if ~ismember(IDName, rsT.Properties.VariableNames)
                    N = size(rsT, 1);
                    caseids =arrayfun(@(x)(num2str(x)), [1:N]', 'UniformOutput',false);
                    rsT.(IDName) = caseids; 
                end
                
                CaseID = rsT.(IDName);
                if iscell(CaseID)
                   CaseID = cellfun(@(x)(str2double(x)), CaseID, 'uniformoutput', true); 
                end
                
                if ismember('SubCaseID', rsT.Properties.VariableNames)
                    SubCaseID = rsT.('SubCaseID');
                else
                    SubCaseID = CaseID;
                end
                
                if iscell(SubCaseID)
                   SubCaseID = cellfun(@(x)(str2double(x)), SubCaseID, 'uniformoutput', true); 
                end

                if ismember('ImageID', rsT.Properties.VariableNames)
                    ImageID = rsT.ImageID; 
                    if iscell(ImageID)
                        ImageID = cellfun(@(x)(str2double(x)), ImageID, 'uniformoutput', true); 
                    end
                else
                    ImageID =[1:N]';
                end

                IsTemplate = zeros(N, 1, 'like', false);
                IsOutlier  = zeros(N, 1, 'like', false);
                IsTest = zeros(N, 1, 'like', false);

                OutlierCases=StructBase.getfieldx_default(trainStr, 'OutlierCases', []);
                if ~isempty(OutlierCases)
                    IsOutlier( OutlierCases)=1;
                elseif ismember('IsOutlier', varnames)
                    IsOutlier = rsT.('IsOutlier'); 
                end

                TestMod = StructBase.getfieldx_default(trainStr, 'TestMod', 0);
                if TestMod>0
                    I = arrayfun(@(x)(mod(x, TestMod)==0), SubCaseID); 
                    IsTest=IsTest|I;
                elseif ismember('IsTest', varnames)
                    IsTest = rsT.('IsTest'); 
                end

                TemplateMod = StructBase.getfieldx_default(trainStr, 'TemplateMod', 0);
                if TemplateMod>0
                    I = arrayfun(@(x)(mod(x, TemplateMod)==1), SubCaseID); 
                    IsTemplate=IsTemplate|I;
                elseif ismember('IsTemplate', varnames)
                    IsTemplate = rsT.('IsTemplate'); 
                end
                
                NumTemplatePerClass = StructBase.getfieldx_default(trainStr, 'NumTemplates', []);
                if ~isempty(NumTemplatePerClass)
                    ROIIndex = rsT.ROIIndex;
                    indexes = unique(ROIIndex);
                    templates =[];
                    for k=1:numel(indexes)
                        templates1=find(IsTemplate & ROIIndex==indexes(k),  NumTemplatePerClass, 'first');
                        templates=cat(1,  templates,  templates1);
                    end
                    IsTemplate1 = zeros(N, 1, 'like', false);
                    IsTemplate1(templates)=1; 
                    IsTemplate= IsTemplate1;
                end

                nnunetroot = StructBase.getfieldx(trainStr, 'nnUNETRoot');  
                taskname   = StructBase.getfieldx(trainStr,   'TaskName');  
                labels     = StructBase.getfieldx(trainStr,   'LabelName');   
                InputChannel=StructBase.getfieldx(trainStr, 'InputChannel');   
                modality = StructBase.getfieldx(trainStr,   'InputModality');
                 
                trainclasses=StructBase.getfieldx_default(trainStr, 'TrainClass', 'nnUNetTrainerV2_ep4000_nomirror|nnUNetTrainerV2');

                if ischar(InputChannel)
                    InputChannel = strsplit(InputChannel, '|'); 
                end
                imgfiles=[];
                for k=1:numel(InputChannel)
                    chfiles= daemon.DcmRTAtlasServer.Table2FullFiles(rsT, InputChannel{k}, datarootfolder); 
                    imgfiles= cat(2, imgfiles, chfiles);
                end

                LabelFile=StructBase.getfieldx(trainStr,    'LabelFile');
                if ischar(LabelFile)
                    labelfiles = daemon.DcmRTAtlasServer.Table2FullFiles(rsT, LabelFile, datarootfolder); 
                elseif iscell(LabelFile)
                    labelfiles=[];
                    for k=1:numel(LabelFile)
                        chfiles= daemon.DcmRTAtlasServer.Table2FullFiles(rsT, LabelFile{k}, datarootfolder); 
                        labelfiles= cat(2, labelfiles, chfiles);
                    end
                end
                                
                checkfile = StructBase.getfieldx_default(trainStr, 'CheckFileExist', 1);   
                nonExistI =zeros(N, 1); 
                if checkfile
                    %N = size(labelfiles, 1);
                    I = zeros(N, 1, 'like', false);
                    I1= ones(N, 1, 'like', false);
                    for k=1:size(labelfiles, 2)
                        I1 = I1 & cellfun(@(x)(~exist(x, 'file')), labelfiles(:, k));
                    end
                   
                    I  = I|I1;
                    for k=1:numel(InputChannel)
                        I1 =cellfun(@(x)(~exist(x, 'file')), imgfiles(:, k));
                        I  = I|I1;
                    end
                    %testI=testI|I;
                    nonExistI = I; 
                end  

                % templateguide =  StructBase.getfieldx_default(trainStr,   'TemplateGuidance', 0);
                %NumberOfTemplates = StructBase.getfieldx_default(templateguide, 'NumberOfTemplates', 0);
                extraopt = StructBase.getfieldx(trainStr,'OtherOption');
                if isstruct(extraopt)
                    extraopt=OptionsMap.struct2options(extraopt); 
                end
              
                if ismember('ClassLabel', varnames)
                    LabelClass=rsT.ClassLabel;
                elseif ismember('LabelClass', varnames)
                    LabelClass=rsT.LabelClass;
                else
                    LabelClass=ones(N, 1); 
                end

                IsValid = ~(nonExistI|IsOutlier); 
                caseids      = rsT.CaseID;
                testI        = IsTest & IsValid; 
                templateI    = IsTemplate & IsValid;
                %trainI       = ~IsTest &  ~IsTemplate & IsValid; 
                if ismember('IsTrain',rsT.Properties.VariableNames)
                    trainI       = rsT.IsTrain; 
                else
                    trainI       = ~IsTest &  ~IsTemplate & IsValid; 
                end
                dataset = nnunet.DatasetJson.PrepareTrain_templateguide(taskname,  modality, labels, imgfiles, labelfiles,  ...
                    {'LabelClass', LabelClass}, {'IsTemplate', templateI},...
                    {'nnunet.root',nnunetroot}, {'ImageID', caseids}, {'WithTestData', 0}, {'trainclasses',trainclasses},{'IsTrain', trainI}, {'IsTest', testI}, extraopt);
        end

        function roimaksfiles = Table2FullFiles(rsT, roimaskfname, datarootfolder)
            N=size(rsT, 1); 
            roimaksfiles=cell(N, 1);
            for n= 1:N
                info = table2struct(rsT(n, :));
                roimaksfiles{n, 1} = daemon.DcmRTServer.toFullFile(roimaskfname, info,datarootfolder);
            end
        end

        function outT = DecomposeTables(rsT, tasks, datarootfolder)
            if isstruct(tasks)
                tasks = arrayfun(@(x)(x), tasks, 'uniformoutput', false); 
            end
            
            for k=1:numel(tasks)
                outT{k}=daemon.DcmRTAtlasServer.DecomposeTable(rsT, tasks{k}, datarootfolder);
            end
        end
        
        function rsT = DecomposeTable(rsT, task, datarootfolder)
            filter = StructBase.getfieldx(task,'TableFilter'); 
            if isempty(filter)
                return;
            end
            
            dbfilter = xls.TableFilter; 
            dbfilter.AddTableFilter(filter);
            [~, rsT] = dbfilter.FilterTable(rsT); 
            outxlsfile  = StructBase.getfieldx(task,'OutputXlsFile'); 
            if ~isempty(outxlsfile)
                outsheet = StructBase.getfieldx_default(task,'OutputSheet', '');
                outxlsfile = daemon.DcmRTServer.toFullFile(outxlsfile, [],datarootfolder);
                xls.TableBase.WriteTable(rsT, outxlsfile, 'sheet',outsheet);  
            end
        end
        
        

        function [T, T2] = ConvertRSNames(T0, nomen, outxlsfile)
%             nomenfile = 'C:\ARTDaemon\Glioma\ServiceDcmConverter\tasksdef\Nomenclature_TG263.xlsx';
%             xlsfile   = 'C:\ARTDaemon\Glioma\Outputs\RTPlanDB.xlsx';
%             T0        = xls.TableBase.ReadTable(xlsfile, {'table.sheetname', 'RTSTRUCT'});
%             nomen     = ai.rtstruct.Nomenclature({'NomenclatureTable.file', nomenfile}, {'NomenclatureTable.sheet', 'OAR'});
            [T] = nomen.ParseROISet(T0.StructureNames);
            T.PatientID = T0.PatientID; 
            T.RSUID     = T0.SOPInstanceUID;
            T = movevars(T, 'PatientID', 'before', 1);
            T = movevars(T, 'RSUID', 'before', 2);

            StandardID=T.StandardID;
            MatchROIName = T.MatchROIName;
            N = numel(StandardID);
            stdids =[]; roinames = [];
            for n=1:N
                stdids = cat(2, stdids, strsplit(StandardID{n}, '|'));
                roinames = cat(2, roinames, strsplit(MatchROIName{n}, '|'));
            end
            [uniids, IA, IC]=unique(stdids);
            for k=1:numel(uniids)
                I = find(IC==k);
                counts(k) = numel(I);
                OrigROIName{k} = xls.TableBase.Content2Str(unique(roinames(I)));
            end
            T2 = table; 
            T2.StandardID =uniids(:);
            T2.ROICount = counts(:);
            T2.MatchROIName=OrigROIName(:);
            %T2 = sortrows(T2, 'ROICount', 'descend');
            if exist('outxlsfile', 'var') && ~isempty(outxlsfile)
                xls.TableBase.WriteTable(T, outxlsfile, 'sheet', 'MatchedROINames');
                xls.TableBase.WriteTable(T2, outxlsfile, 'sheet', 'ROICount');
            end
        end
        
        function atlas0 = CollectAtlasInfo(oarnames, fnames, outxlsfile, roiinfonames, varargin)
            options = OptionsMap(varargin{:});
            caseids = arrayfun(@(x)(num2str(x)), 1:numel(fnames), 'UniformOutput',false);
            caseids = caseids(:);
            if ischar(roiinfonames)
                roiinfonames = strsplit(roiinfonames, '|');
            end
            %roistatnames=cat(2, roiinfonames, {'BBStart', 'BBEnd', 'BBSize', 'VolHeader'});
            %roistatnames=union(roiinfonames, {'BBStart', 'BBEnd', 'BBSize', 'VolHeader'}, 'stable');
            atlas0  = ai.atlas.AtlasStat.CollectAtlasInfos(fnames, oarnames, outxlsfile, {'ROIInfoNames',roiinfonames}, varargin{:});
%             refcenter = options.getoptioni('RefCenter'); 
%             roistatnames=union(roiinfonames, {'VolHeader'}, 'stable');
%             ai.atlas.AtlasStat.CollectAtlasStat(outxlsfile, refcenter, {'ROIStatNames', roistatnames}, varargin{:});
        end
        
        function ShowAtlas(taskdef, info, datarootfolder, varargin) 
            figfolder = StructBase.getfieldx(taskdef, 'FigureFolder');
            figfolder = daemon.DcmRTServer.toFullFile(figfolder, info, datarootfolder);
            if ~exist(figfolder, 'dir')
                mkdir(figfolder);
            end

            caseid    = StructBase.getfieldx(info, 'CaseID'); 
            img =[]; roimask=[];
            imgfname  = StructBase.getfieldx(taskdef, "AtlasImage"); 
            
            if ~isempty(imgfname)
                imgfname  = daemon.DcmRTServer.toFullFile(imgfname, info,datarootfolder);
                img = VolHeaderImage(imgfname);
            end
            
            roimaskfname = StructBase.getfieldx(taskdef, "AtlasROIMask"); 

            if ~isempty(roimaskfname)
                roimaskfname = daemon.DcmRTServer.toFullFile( roimaskfname, info,datarootfolder);
                roimask = ROIMaskImage(roimaskfname);
            end
            
            if isempty(roimask)
                img.imview({'FigureFile', [figfolder caseid '.png']});
            elseif ~isempty(roimask.imageType) && strcmpi(roimask.imageType, 'labelmask')
                img.imview_labelmask(roimask, {'FigureFile', [figfolder caseid '.png']});
            else
                img.imview({'roi1', roimask}, {'FigureFile', [figfolder caseid '.png']});
            end

            if ~isempty(roimask)
                DecomposeROIs= StructBase.getfieldx(taskdef, 'DecomposeROIs');
                if ~isempty(DecomposeROIs)
                    roinames=[];
                    if ischar(DecomposeROIs)
                        roinames = strsplit(DecomposeROIs, '|');
                    elseif isstruct(DecomposeROIs)
                        roinames = StructBase.getfieldx(DecomposeROIs, 'ROIName');
                        if ~isempty(roinames)
                            roinames=strsplit(roinames, '|');
                        end
                    elseif isnumeric(DecomposeROIs) &&DecomposeROIs>0
                        roinames = roimask.ROINames; 
                    end
                    
                    for m=1:numel(roinames)
                        roiname = roinames{m};
                        subfolder = DosUtil.mksubdir(figfolder, roiname);
                        submask = roimask.GetSubMaskImage(roiname);
                        img.imview_labelmask(submask, {'FigureFile', [subfolder caseid '.png']});
                    end
                end
            end
        end
        
        function CreateAtlas(taskdef, info, datarootfolder, varargin)           
            AtlasCS =[];
            atlasstr = StructBase.getfieldx(taskdef, "AtlasCS"); 
            options = OptionsMap(varargin{:});
            taskdeffolder = options.getoptioni('TaskDefFolder');
            
            usetemplateFOR = StructBase.getfieldx_default(taskdef, "UseTemplateFOR", 0); 
            outfolder = StructBase.getfieldx(taskdef, 'OutputFolder');
            outfolder = daemon.DcmRTServer.toFullFile(outfolder, info, datarootfolder);
            mkdir(outfolder);
            options.setOption('OutputFolder', outfolder);
            TemplateCS= StructBase.getfieldx(taskdef, "TemplateVolHeader");
            if ~isempty(TemplateCS) && ischar(TemplateCS)
                vhfname = daemon.DcmRTServer.toFullFile(TemplateCS, info,datarootfolder);
                %TemplateCS= VolHeader(vhfname);
                TemplateCS= ai.atlas.AtlasCS(vhfname);
            end
%             if ~isempty(atlasstr)
%                vhfile  = StructBase.getfieldx(atlasstr, "TemplateVolHeaderFile"); 
%                vhfile  = daemon.DcmRTServer.toFullFile(vhfile, info,taskdeffolder);
%                AtlasCS = VolHeader; AtlasCS.readJsonHeader(vhfile);
%                TemplateCS = VolHeader(AtlasCS);
%                RefCenter=StructBase.getfieldx(atlasstr, "RefCenter"); 
%                RefCenter=daemon.DcmRTAtlasServer.ParseRefCenter(RefCenter, info, datarootfolder);
%                if ~isempty(RefCenter) && isnumeric(RefCenter)
%                    AtlasCS.ResetCenterVoxelDcmCoor(RefCenter);
%                end
%                AtlasCS.writeJsonHeader([outfolder 'AtlasCS.json']); 
%             end
            if ~isempty(atlasstr)
%                vhfile  = StructBase.getfieldx(atlasstr, "TemplateVolHeaderFile"); 
%                vhfile  = daemon.DcmRTServer.toFullFile(vhfile, info,taskdeffolder);
%                AtlasCS = VolHeader; AtlasCS.readJsonHeader(vhfile);
%                TemplateCS = VolHeader(AtlasCS);
%                RefCenter=StructBase.getfieldx(atlasstr, "RefCenter"); 
%                RefCenter=daemon.DcmRTAtlasServer.ParseRefCenter(RefCenter, info, datarootfolder);
%                if ~isempty(RefCenter) && isnumeric(RefCenter)
%                    AtlasCS.ResetCenterVoxelDcmCoor(RefCenter);
%                end
               atlasstr.Info = info; 
               AtlasCS=ai.atlas.AtlasCS.Factory(atlasstr); %TemplateCS = VolHeader(AtlasCS);
               AtlasCS.writeJsonHeader([outfolder 'AtlasCS.json']); 
            end
            options.setOption('UseTemplateFOR', usetemplateFOR);
            options.setOption('TemplateVolHeader', TemplateCS);

            
            imagetsks = StructBase.getfieldx(taskdef, "AtlasImage"); 
            if isstruct(imagetsks)
                imagetsks = arrayfun(@(x)(x), imagetsks, 'UniformOutput',false);
            end
            for k=1:numel(imagetsks)
                imgfnames{k} = daemon.DcmRTAtlasServer.CreateAtlasImage(imagetsks{k}, info, AtlasCS, datarootfolder, options);
            end
            
            masktsks = StructBase.getfieldx(taskdef, "AtlasROIMask"); 
            if isstruct(masktsks)
                try
                masktsks = arrayfun(@(x)(x), masktsks, 'UniformOutput',false);
                catch
                end
            end
            for k=1:numel(masktsks)
                try
                roimaskfnames{k} =daemon.DcmRTAtlasServer.CreateAtlasROIMask(masktsks{k}, info, AtlasCS, datarootfolder, options);
                catch
                end
            end

            showatlas = StructBase.getfieldx(taskdef, "ShowAtlas");
            if ~isempty(showatlas)
                trainfolder =DosUtil.SimplifyPath([ outfolder  '../']);
                CaseID = StructBase.getfieldx(info, 'CaseID');
                DecomposeROIs=StructBase.getfieldx_default(showatlas, "DecomposeROIs", 0);
                for k=1:numel(imagetsks)
                    imagetsk = imagetsks{k};
                    ImageFileName=StructBase.getfieldx(imagetsk, 'OutputName');
                    ImageModality=StructBase.getfieldx_default(imagetsk, 'ImageModality', 'CT'); 
                    for m=1:numel(masktsks)
                        try
                        masktsk = masktsks{m};
                        MaskFileName0=[StructBase.getfieldx(masktsk, 'OutputName')];
                        roiname = [ImageFileName '#' MaskFileName0];
                        OutMaskType =  StructBase.getfieldx_default(masktsk,  'OutMaskType', 'roimask');
                        if strcmpi(OutMaskType, 'labelmask')
                            MaskFileName=[MaskFileName0 '_label'];
                        else
                            MaskFileName=MaskFileName0;
                        end
                        FigureFolder=mksubdir(trainfolder, ['Figure\' roiname]);
                        options = OptionsMap({'ImageType', ImageModality}, ...
                            {'FigureFolder', FigureFolder}, ...
                            {'ImageFileName', [ImageFileName '.nii.gz']}, {'imviewmode', OutMaskType},...
                            {'LabelFileName', [MaskFileName '.nii.gz']}); 
                        daemon.DcmRTAtlasServer.ShowTrainData(trainfolder, CaseID, roiname, options);
                        if DecomposeROIs
                            options1 = OptionsMap(options);
                            options1.setOption('imviewmode', 'labelmask');
                            ROIs=StructBase.getfieldx(masktsk, 'ROIs'); 
                            if isempty(ROIs)
                                continue;
                            end
                            if ischar(ROIs)
                                ROIs = strsplit(ROIs, '|');
                            end
                            for n=1:numel(ROIs)
                                roiname1 = ROIs{n};
                                MaskFileName1 = [MaskFileName0 '\' roiname1];
                                FigureFolder1 = mksubdir(FigureFolder, roiname1);
                                options1.setOption('FigureFolder', FigureFolder1);
                                options1.setOption('LabelFileName',  [MaskFileName1 '.nii.gz']);
                                daemon.DcmRTAtlasServer.ShowTrainData(trainfolder, CaseID, roiname1, options1);
                            end
                        end
                        catch
                        end
                    end
                end
            end
        end

        function imgfname = CreateAtlasImage(task, info, atlasvh, datarootfolder, varargin)
            options       = OptionsMap(varargin{:});
            imgfname      = StructBase.getfieldx(task, 'ImageFileName');
            taskdeffolder = options.getoptioni('TaskDefFolder');
            outfolder     = options.getOption('OutputFolder', '');
            UseTemplateFOR= options.getOption('UseTemplateFOR');
            TemplateVolHeader=options.getOption('TemplateVolHeader');

            if ~isempty(imgfname)
                imgfname = daemon.DcmRTServer.toFullFile(imgfname, info,datarootfolder);
                img = VolHeaderImage(imgfname);
                if ~isempty(atlasvh)
                    %img.ReformCS(atlasvh,  {'interpmethod', 'cubic'});
                    ai.atlas.AtlasCS.ReformImage2AtlasCS(img, atlasvh);
                end
                if UseTemplateFOR
                    img.ResetDicomAffineMatrix(TemplateVolHeader.DicomAffineMatrix);
                end
                outfname = StructBase.getfieldx(task, 'OutputName');
                img.writeNiftiImage([outfolder outfname]);
            end
        end

        function roimaskfilename = CreateAtlasROIMask(task, info, atlasvh, datarootfolder, varargin)
            imgfname = StructBase.getfieldx(task, 'ImageFileName');
            if isempty(imgfname)
                return; 
            end

            options   = OptionsMap(varargin{:});
            outfolder = options.getOption('OutputFolder', '');
            nomenstr  = StructBase.getfieldx(task, 'Nomenclature'); 
            taskdeffolder = options.getoptioni('TaskDefFolder');
            nomen=daemon.DcmRTAtlasServer.ParseNomenclature(nomenstr, taskdeffolder);
            imgfname = daemon.DcmRTServer.toFullFile(imgfname, info,datarootfolder);
            img = ROIMaskImage(imgfname);
            if ~isempty(atlasvh)
                %img.ReformCS(atlasvh,  {'interpmethod', 'cubic'});
                ai.atlas.AtlasCS.ReformImage2AtlasCS(img, atlasvh);
            end
            if ~isempty(nomen)
                IncludeUnMatched = StructBase.getfieldx_default(nomenstr, 'IncludeUnMatched', 1);
                nomen.ConvertROIMaskImage(img, {'IncludeUnMatched', IncludeUnMatched });
            end
            outputtype = StructBase.getfieldx_default(task,'OutMaskType',  'labelmask');
            rois = StructBase.getfieldx(task,"ROIs"); roimask=[];
            if ~isempty(rois)
                %roimask = img.GetSubMaskImage(rois, {'outputmasktype', outputtype});
                %roimask.ROIStatTable_regionprops();
                roimask = img.GetSubMaskImage(rois);
            else
                roimask = img; 
            end

            resetstr = StructBase.getfieldx(task,'ResetROINames_LR');
            if ~isempty(resetstr)
               roimask.ResetROINames_LR(resetstr);
            end

            UseTemplateFOR = options.getOption('UseTemplateFOR');
            TemplateVolHeader=options.getOption('TemplateVolHeader');
            if UseTemplateFOR
                    roimask.ResetDicomAffineMatrix(TemplateVolHeader.DicomAffineMatrix);
            end
            roimaskfilename=[];
            if ~isempty(roimask)
                roimask.ROIStatTable_regionprops('ROICenter|ROIVolume|BBCenter|BBSize');
                outfname = StructBase.getfieldx(task, 'OutputName');
                DecomposeROIs=StructBase.getfieldx(task, 'DecomposeROIs');
                if DecomposeROIs
                    roinames = roimask.ROINames; 
                    subfolder = DosUtil.mksubdir(outfolder, outfname);
                    for k=1:numel(roinames)
                        name    =roinames{k};
                        submask = roimask.GetSubMaskImage(name, {'outputmasktype', 'labelmask'});                       
                        submask.writeNiftiImage([subfolder  name]);
                    end
                end
                roimaskfilename = [outfolder outfname];
                roimask.writeNiftiImage(roimaskfilename);
                if strcmpi(outputtype, 'labelmask') && ~strcmpi(roimask.imageType, outputtype)
                    labelmask = roimask.GetSubMaskImage(roinames,  {'outputmasktype', 'labelmask'});
                    labelmask.writeNiftiImage([roimaskfilename '_label']);
                end
            end

        end

        function ShowTrainData(trainfolder, CaseID, roiname, varargin)
            options = OptionsMap(varargin{:});
            figfolder = options.getOption('FigureFolder');
            if isempty(figfolder)
                figfolder0 = mksubdir(trainfolder, 'Figure');
                subfoldername = options.getOption('SubFigureFolderName', roiname);
                figfolder =  mksubdir(figfolder0, subfoldername);
            end

            figfolder=mksubdir(figfolder);
            imageType = options.getOption('ImageType', '');
            if strcmpi(imageType, 'ct')
                options.setUndefinedOption('windowLow', 800);
                options.setUndefinedOption('windowHigh', 1200);
            elseif strcmpi(imageType, 'mr')
                options.setUndefinedOption('windowLow', -1);
                options.setUndefinedOption('windowHigh', 1);
            end
            imviewmode = options.getoptioni('imviewmode', 'labelmask');

            imgname = options.getOption('ImageFileName', 'image.nii.gz'); 
            labelname = options.getOption('LabelFileName', ['roimask\' roiname '.nii.gz']); 
            if ischar(CaseID)
                CaseID = strsplit(CaseID, '|');
            end
            for k=1:numel(CaseID)
                caseid = CaseID{k};
                imgfile = [trainfolder caseid '\', imgname];
                lablefile = [trainfolder caseid '\' labelname];
                if exist(imgfile, 'file') && exist(lablefile, 'file')
                    img = VolHeaderImage(imgfile);
                    if strcmpi(imageType, 'mr')
                        img.zScoreNormalizeData();
                    end
                    labelmask = ROIMaskImage(lablefile);
                    figfile = [figfolder caseid '.png'];
                    switch imviewmode
                        case 'labelmask'
                            img.imview_labelmask(labelmask, {'FigureFile',figfile}, options);
                        case 'roi'
                            img.imview({'roi1', labelmask}, {'FigureFile',figfile}, options);
                    end
                end
            end
        end
    end
end

