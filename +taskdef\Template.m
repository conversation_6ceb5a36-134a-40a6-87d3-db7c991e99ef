classdef Template <utils.json.TaskDef
    properties

    end

    methods
        function obj = Template(shortname, varargin)
            <EMAIL>(varargin{:});
            obj.SetConfig("TaskDoneIndicator", [shortname '.cfg']); 
            obj.SetConfig("TaskConfigOutput",  [shortname '.cfg']); 
            obj.SetConfig("TaskOutputFolder",  "[TaskOutputFolder]"); 
        end

        function AddInputs(self, inputs)
            % if ischar(inputs)
            %     inputs = strsplit(inputs, '|');
            % end
            if ~isempty(inputs)
                self.SetConfig('Dependency.filename', inputs);
            end
        end

        function AddProcess(self,  optype,  fixedstr, templateparas, listname)
            if ~exist('listname', 'var')
                listname = 'Process';
            end
            process = fixedstr;     
            process.('OperationType')=optype;
            self.AddConfig2List(listname, process, templateparas);
       end

       function Create(tempdef, optype, inputs, fixedstr, templateparas)
            tempdef.AddInputs(inputs);
            tempdef.AddProcess(optype,  fixedstr, templateparas, 'Process');
        end
    end

    methods (Static)
        function tempdef = nnUNETInference(taskdeffname)
            shortname = 'nnUNETInference'; optype = shortname; 
            tempdef = daemon.taskdef.Template(shortname);
            inputs = '[InputImage]';
            fixedstr =[];
            templateparas='ModelOptionFile|InputImage|OutputFile';
            Create(tempdef, optype, inputs, fixedstr, templateparas);
            if exist('taskdeffname', 'var')
                tempdef.writeJson(taskdeffname);
            end
        end

        function tempdef = focal_ListPatient(activedate, taskdeffname)
            shortname = 'Focal_ListPatient'; 
            optype = 'Focal_ListPatient'; 
            tempdef = daemon.taskdef.Template(shortname);
            inputs = '';
            templateparas='';
            Filter = struct('DateTimeRange', activedate);
            fixedstr = struct('Filter', Filter);

            Create(tempdef, optype, inputs, fixedstr, templateparas);
            [folder, name]=fileparts(taskdeffname);
            TaskOutputFolder = DosUtil.SimplifyPath(mksubdir('..\dcm2nifti\', name));
           
            tempdef.SetConfig("TaskOutputFolder", TaskOutputFolder);

            tempdef.writeJson(taskdeffname);
        end
    end
end