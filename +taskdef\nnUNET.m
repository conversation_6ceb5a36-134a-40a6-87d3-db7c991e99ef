classdef nnUNET <daemon.taskdef.TaskDef
    properties

    end

    methods
        function obj = nnUNET(varargin)
            options = OptionsMap(varargin{:});
            <EMAIL>(options);
            obj.SetDefaultCfg();
            %AddProcess_nnUNET(obj);
        end
    end

    methods (Static)
        function InferenceImage(modeloptfile, tasksdeffile, varargin)
             %names = {'ModelOptionFile', 'InputImage', 'OutputFile'};
             modeloptfile = DosUtil.SimplifyPath(modeloptfile);
             opts = OptionsMap(varargin{:});
             preprocess = opts.getOption('PreProcess');
             postprocess = opts.getOption('PostProcess');
             DefaultSettings = opts.getOption('DefaultSettings');
             label_image_type=opts.getOption('label_image_type', 'labelmask'); 
             [modelfolder, modelname, ext]= fileparts(modeloptfile);
             modelname = opts.getOption('ModelName', modelname);
             modelname = matlab.lang.makeValidName(modelname);
             taskdeffolder = [fileparts(tasksdeffile) '/'];
             % preprocess=[];
             % if strcmpi(modality, 'CT')
             %    preprocess = struct('OperationType', 'IntensityMapData', 'MapType', 'linear',...
             %        'IntensityScale', 1, 'IntensityOffset', -1000);
             % end
             taskdefname = ['temp_' modelname];
             obj = daemon.taskdef.nnUNET({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
             ModelOptionFile= modeloptfile;
             InputImage='[InputImage]';
             OutputFile='[OutputFile]';
             dependency = struct("filename", InputImage);
             preprocess = opts.getOption('PreProcess');
             postprocess = opts.getOption('PostProcess');
             %RemoveTiny = struct('OperationType', 'RemoveROIs', 'OperationName', 'RemoveTiny');
             Process=struct('OperationType', 'nnUNETInference',...
                 'ModelOptionFile', ModelOptionFile,...
                 'InputImage', InputImage, 'OutputFile', OutputFile,'PreProcess', preprocess, 'PostProcess', postprocess); 
             obj.Generate(tasksdeffile, dependency, Process, {'DefaultSettings', DefaultSettings});
             
             modality = opts.getOption('InputModality');
             if ~isempty(modality)
                 taskdefname = [modality '_' modelname];
                 obj = daemon.taskdef.nnUNET({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
                 CustomSubFolder=opts.getOption('CustomSubFolder', modelname); 
                 InputImage= opts.getOption('InputImage', '../image.nii.gz');
                 DependentTaskFile=opts.getOption('DependentTaskFile', '../DcmConverter/[Modality].[SeriesInstanceUID].tsk');
                 OutputFile= label_image_type;
                 dependency= struct("filename", InputImage, "taskfilename", DependentTaskFile);
                 Process=struct('OperationType', 'nnUNETInference',...
                     'ModelOptionFile', ModelOptionFile,...
                     'InputImage', InputImage, 'OutputFile', OutputFile,'PreProcess', preprocess, 'PostProcess', postprocess);
                 obj.Generate(tasksdeffile, dependency, Process, {'CustomSubFolder', CustomSubFolder}, {'DefaultSettings', DefaultSettings});
             end
        end

        function Process=AugmentInferenceProcess(ModelOptionFile, augcfg, InputImage, OutputFile)
             modelopts = OptionsMap(ModelOptionFile);
             label_image_type=modelopts.getOption('label_image_type');
             if strcmpi(label_image_type, 'image')
                 preprocess  = struct('OperationType', 'MaskAugmentImage', 'OutMaskImageFile', 'AugmentMask', 'OutAugmentImageFile', 'AugmentImage');
                 preprocess.Augment =  augcfg; 
                 postprocess = struct('OperationType', 'MaskReplaceROI', 'SrcMaskFile',  'AugmentMask.nii.gz', 'SrcImageFile', 'AugmentImage.nii.gz', 'mask_include_mode', 0);
             else
                 preprocess  = struct('OperationType', 'MaskAugmentImage');
                 preprocess.Augment =  augcfg; 
                 postprocess = [];

             end
             m=0;   Process =[]; 
             m=m+1; Process{m}=struct('OperationType', 'nnUNETInference',...
                     'ModelOptionFile', ModelOptionFile,...
                     'InputImage', InputImage, 'OutputFile', OutputFile,'PreProcess', preprocess, 'PostProcess', postprocess);
             m=m+1; Process{m}=struct('OperationType', 'RemoveFile',...
                     'FilePattern', 'Augment*.nii.gz');
        end

        

        function InferenceStudy(modeloptfile, tasksdeffile, varargin)
             %names = {'ModelOptionFile', 'InputImage', 'OutputFile'};
             modeloptfile = DosUtil.SimplifyPath(modeloptfile);
             opts = OptionsMap(varargin{:});
             preprocess = opts.getOption('PreProcess');
             postprocess = opts.getOption('PostProcess');
             label_image_type=opts.getOption('label_image_type', 'labelmask'); 
             [modelfolder, modelname, ext]= fileparts(modeloptfile);
             modelname = opts.getOption('ModelName', modelname);
             modelname = matlab.lang.makeValidName(modelname);
             taskdeffolder = [fileparts(tasksdeffile) '/'];
             %modelopts = OptionsMap(modeloptfile);
             
             InputChannels= opts.getOption('InputChannels');
             InputChannels= strsplit(InputChannels, '|');

             taskdefname = ['temp_' modelname];
             obj = daemon.taskdef.nnUNET({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
             ModelOptionFile= modeloptfile;
             InputImage='[InputImage]';
             OutputFile='[OutputFile]';
             dependency = struct("filename", InputImage);
             
             %RemoveTiny = struct('OperationType', 'RemoveROIs', 'OperationName', 'RemoveTiny');
             Process=struct('OperationType', 'nnUNETInference',...
                 'ModelOptionFile', ModelOptionFile,...
                 'InputImage', InputImage, 'OutputFile', OutputFile, 'ReformCS2Channel',1); 
             obj.Generate(tasksdeffile, dependency, Process);
             StudyFolder = opts.getOption('StudyFolder', '../STUDY.[StudyInstanceUID]/'); 
             modality = opts.getOption('InputModality');
             if ~isempty(modality)
                 taskdefname = [modality '_' modelname];
                 obj = daemon.taskdef.nnUNET({'TaskDefFolder', taskdeffolder}, ...
                        {'TaskDefName', taskdefname});
                 CustomSubFolder=[StudyFolder modelname]; 
                 %InputImage= '../image.nii.gz';
                 InputImage=cellfun(@(x)(['../' x '.nii.gz']), InputChannels, 'UniformOutput', false); 
                 OutputFile= label_image_type;
                 %dependency= struct("filename", InputImage, "taskfilename", "../DcmConverter/[Modality].[SeriesInstanceUID].tsk");
                 dependency=cellfun(@(x)(struct("filename", x)), InputImage,'UniformOutput',false);
                 % for m=1:numel(InputChannels)
                 %    dependency{m} = struct("filename", ['../STUDY.[StudyInstanceUID]/' InputChannels{m} '.nii.gz']);
                 % end
                 Process=struct('OperationType', 'nnUNETInference',...
                     'ModelOptionFile', ModelOptionFile,...
                     'InputImage', xls.TableBase.Content2Str(InputImage), 'OutputFile', OutputFile, 'ReformCS2Channel',1); 
                 obj.Generate(tasksdeffile, dependency, Process, {'CustomSubFolder', CustomSubFolder});
             end
        end


        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        function InferenceDef(tasksdeffile, taskdefname, modeloptfile, varargin)
             modeloptfile=DosUtil.SimplifyPath(modeloptfile);
             ops = OptionsMap(varargin{:});
             ExtraDef = ops.getOption('ExtraDef'); 
             if isstruct(ExtraDef)
                ExtraDef = OptionsMap.struct2options(ExtraDef);
             end
             taskdeffolder = [fileparts(tasksdeffile) '/'];
             obj = daemon.taskdef.nnUNET({'TaskDefFolder', taskdeffolder}, ...
                    {'TaskDefName', taskdefname});
             [Process, dependency, CustomSubFolder]=daemon.taskdef.nnUNET.InferenceStruct(modeloptfile, taskdefname, ops, ExtraDef);     
             DefaultSettings= ops.getOption('DefaultSettings');
             dependency = ops.getOption('Dependency', dependency);
             obj.Generate(tasksdeffile, dependency, Process, {'DefaultSettings', DefaultSettings}, {'CustomSubFolder', CustomSubFolder}, ExtraDef);
        end
        
        function InferenceDef_ExtraSource(tasksdeffile, taskdefname, modeloptfile, extrasource, varargin)
            ops = OptionsMap(varargin{:});
            prefix = extractBefore(taskdefname, '_');
            m=0;   modality=[]; InputImage=[]; ModalityUID=[]; dependency=[];
            m=m+1; modality{m} = prefix; ModalityUID{m}   =[modality{m} '.[SeriesInstanceUID]'];
            extrasource=StructBase.toCell(extrasource);
            for k=1:numel(extrasource)
                m=m+1; 
                modality{m}=StructBase.getfieldx(extrasource{k}, 'DBTableName');
                if m==2
                    ModalityUID{m} = [modality{m} '.{SeriesInstanceUID}'];
                elseif m==3
                    ModalityUID{m} = [modality{m} '.(SeriesInstanceUID)'];
                elseif m==4
                    ModalityUID{m} = [modality{m} '.<SeriesInstanceUID>'];
                end
            end
            
            for m=1:numel(modality)
                 InputImage{m}= ['../../' ModalityUID{m} '/image.nii.gz'];
                 dependency{m}= struct('filename', InputImage{m},...
                 'taskfilename', ['../DcmConverter/DCMCONVERT_' modality{m} '/' ModalityUID{m} '.tsk']);
            end
            InputImage=xls.TableBase.Content2Str(InputImage);
            ExtraDef = struct('ExtraSource', extrasource);
            InferenceCfg = struct("ReformCS2Channel", 1);
            InputImage=ops.getOption('InputImage', InputImage);
            dependency=ops.getOption('Dependency', dependency);
            daemon.taskdef.nnUNET.InferenceDef(tasksdeffile, taskdefname, modeloptfile, {'InputImage', xls.TableBase.Content2Str(InputImage)},...
                {'Dependency', dependency}, {'InferenceCfg', InferenceCfg}, {'ExtraDef', ExtraDef });
        end

        function AugmentInferenceDef(tasksdeffile, taskdefname, modeloptfile, augcfg, varargin)
            modelopts = OptionsMap(modeloptfile);
             label_image_type=modelopts.getOption('label_image_type');
             if strcmpi(label_image_type, 'image')
                 preprocess  = struct('OperationType', 'MaskAugmentImage', 'OutMaskImageFile', 'AugmentMask', 'OutAugmentImageFile', 'AugmentImage');
                 preprocess.Augment =  augcfg; 
                 postprocess = struct('OperationType', 'MaskReplaceROI', 'SrcMaskFile',  'AugmentMask.nii.gz', 'SrcImageFile', 'AugmentImage.nii.gz', 'mask_include_mode', 0);
                 augmentprocess=struct('OperationType', 'RemoveFile',...
                     'FilePattern', 'Augment*.nii.gz');
             else
                 preprocess  = struct('OperationType', 'MaskAugmentImage');
                 preprocess.Augment =  augcfg; 
                 postprocess = [];
                 augmentprocess=[];
             end
             InferenceCfg = struct('PreProcess', preprocess, 'PostProcess', postprocess);
             daemon.taskdef.nnUNET.InferenceDef(tasksdeffile, taskdefname, modeloptfile, {'InferenceCfg',InferenceCfg},...
                {'augmentprocess', augmentprocess});
        end

        function [Process, dependency, CustomSubFolder]=InferenceStruct(ModelOptionFile, taskdefname, varargin)
            modelopts = OptionsMap(ModelOptionFile);
            label_image_type=modelopts.getOption('label_image_type', 'labelmask');
            ops    = OptionsMap(varargin{:});
            prefix = extractBefore(taskdefname, '_');
            subtaskname=extractAfter(taskdefname, '_');
            %astemp = strcmpi(prefix, 'temp');
            astemp = startsWith(prefix, 'temp');
            % preprocess  = ops.getOption('PreProcess');
            % postprocess = ops.getOption('PostProcess');
            if ~astemp
                 CustomSubFolder=ops.getOption('CustomSubFolder', subtaskname); 
                 modality=prefix; 
                 InputImage=ops.getOption('InputImage', '../image.nii.gz');
                 OutputFile=ops.getOption('OutputFile',label_image_type);
                 dependency= struct("filename", InputImage, "taskfilename", ['../DcmConverter/DCMCONVERT_' modality '/[Modality].[SeriesInstanceUID].tsk']);
                 if strcmpi(label_image_type, 'labelmask')
                      listname = 'StructSet';
                      associateinfo=struct('ID', subtaskname, ...
                     "LabelMaskFileName", [CustomSubFolder, '/'  OutputFile]);
                 else
                     listname = 'ImageSet';
                     associateinfo=struct('ID', subtaskname, "SeriesDescription", subtaskname, "Modality", subtaskname, ...,
                         'SeriesDate', '[SeriesDate]',...
                         "ImageFileName", [CustomSubFolder, '/'  OutputFile]);
                 end
                AssociateStruct2List = struct('OperationType','AssociateStruct',...
                    'AssociateFileName', '../dcmdata.cfg', 'ListName', listname, 'Info', associateinfo);
            else
                 %TaskOutputFolder='[TaskOutputFolder]'; 
                 InputImage='[InputImage]';
                 OutputFile='[OutputFile]';
                 %taskdefname = ['temp_' taskdefname0];
                 CustomSubFolder=''; 
                 dependency= struct("filename", InputImage);
                 AssociateStruct2List=[];
            end
            m=0; 
            nnUNETProcess=struct('OperationType', 'nnUNETInference',...
                 'ModelOptionFile', ModelOptionFile,...
                 'InputImage', InputImage, 'OutputFile', OutputFile); 
            InferenceCfg=ops.getOption('InferenceCfg');
            if ~isempty(InferenceCfg)
                fns = fieldnames(InferenceCfg);
                for k=1:numel(fns)
                    name = fns{k};
                    nnUNETProcess.(name)=InferenceCfg.(name); 
                end
            end
            m=m+1; Process{m}=nnUNETProcess; 
            augmentprocess=ops.getOption('augmentprocess');
            if ~isempty(augmentprocess)
                Process = cat(2,  Process, StructBase.toCell(augmentprocess)); 
                m=numel(Process);
            end
            
            if ~isempty(AssociateStruct2List)
                m=m+1; Process{m}   = AssociateStruct2List;
            end
        end
    end
end