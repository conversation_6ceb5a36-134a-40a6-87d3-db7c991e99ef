{
  "Process": {
	  "OperationType": "CalcInitPlan",	
	  "Input": {
		"SrcMotion": "[InitSrcMotionFile]",
		"BixelMap": "[InitBixelFile]",
		"OptMask": "[OptMaskFile]",
		"PredDose": "[PredDoseFile]"
	  },
	  "Output": {
		"BixelFile": "[OutBixelFile]",
		"FluenceFile": "[OutFluenceFile]",
		"SrcMotionFile": "[OutMotionFile]"
		"DoseFile": "[OutDoseFile]"
	  },
	"AppOptions": [
		{
		  "OptionName": "exefile",
		  "OptionValue": "[ProjectionExeFile]"
		},
		{
		  "OptionName": "in.RTBeamType",
		  "OptionValue": "[RTBeamType]"
		},
		{
		  "OptionName": "in.fluence.field.x",
		  "OptionValue": "[FieldSizeX]"
		},
		{
		  "OptionName": "in.fluence.field.y",
		  "OptionValue": "[FieldSizeY]"
		},
		{
		  "OptionName": "in.fluence.bixel.x",
		  "OptionValue": "[PixelSize]"
		},
		{
		  "OptionName": "in.fluence.bixel.y",
		  "OptionValue": "[LeafWidth]"
		},
		{
		  "OptionName": "in.fluence.pixel.x",
		  "OptionValue": "[PixelSize]"
		},
		{
		  "OptionName": "in.fluence.pixel.y",
		  "OptionValue": "[PixelSize]"
		},
		{
			"OptionName": "out.initaper.motion.file",
			"OptionValue": "motion.txt"
		},
		{
			"OptionName": "out.initaper.bixelmap.file",
			"OptionValue": "bixelmap"
		},
		{
			"OptionName": "initapers.target.margin",
			"OptionValue": "[TargetMargin]"
		},
		{
			"OptionName": "in.initaper.gantry.angles",
			"OptionValue": "[GantryAngle]"
		},
		{
			"OptionName": "in.initaper.usetargetcenter",
			"OptionValue": "[UseTargetCenter]"
		}
	  ]
  },
  "DefaultSettings": {
    "ProjectionExeFile": "C:/ArtDaemon/distribute/wtkapp/bin/appBBFMO.exe",
    "OutMotionFile": "OptSrcMotion.json",
	"OutBixelFile": "OptBixelmap",
	"OutFluenceFile": "OptFluencemap",
	"OutDoseFile": "OptDose",
	"TargetMargin": "0.5",
	"LeafWidth": "0.5",
	"PixelSize": "0.1",
	"FieldSizeX": "40",
	"FieldSizeY": "40",
	"GantryAngle": "",
	"FluenceFile": "",
	"DensityFile": "",
	"InitSrcMotionFile": "",
	"InitBixelMapFile": "",
	"PredDoseFile": "",
	"UseTargetCenter": "1"
  }
}